type Deal {
    id: ID!
    title: String!
    description: String!
    deal_type: DealType!
    partner_place: PartnerPlace!
    max_saving: Float
    max_usage_per_day: Int!
    service_type: Tag
    available_slots: [DealDateTimeSlot!]!
    reuse_limit_days: Int!

    service_types: [Tag]!
    myDeals(orderBy: [OrderByClause!] @orderBy): [MyDeal]!
        @paginate(builder: "App\\GraphQL\\Types\\Deal\\MyDeal")
        @whereAuth(relation: "user")
        @guard
}

type DealDateTimeSlot {
    "Next 7 calender days"
    date: String!

    "allowed per day - reserved"
    available_seats: Int!
    slots: [DealTimeSlot!]!
}

type DealTimeSlot {
    from: String!
    to: String!
}

enum DealType {
    FREE_ITEM_WITH_PURCHASE
    TWO_FOR_ONE
    AED_40_DISCOUNT
    ONE_AED_SPECIAL
}

type MyDeal @model(class: "App\\Models\\UserDeal") {
    id: ID!
    deal: Deal!
    status: MyDealStatusEnum!
    reserved_at: DateTime
    redeemed_at: DateTime
    reuse_after: DateTime
    reserve_slot: MyDealDateTimeSlot!
    deal_snapshot: DealSnapshot
}

enum MyDealStatusEnum {
    UPCOMING @enum(value: "upcoming")
    REDEEMABLE @enum(value: "redeemable")
    NO_SHOW @enum(value: "no-show")
    REDEEMED @enum(value: "redeemed")
}

type MyDealDateTimeSlot {
    date: String!
    slot: DealTimeSlot!
}

type DealSnapshot {
    title: String!
    description: String!
    deal_type: DealType!
    service_options: [Tag!]!
    max_saving: Float
    max_usage_per_day: Int!
    reuse_limit_days: Int!
}

extend type PartnerPlace {
    deals: [Deal!]! @hasMany(type: PAGINATOR)
}

extend type User {
    myDeals: [MyDeal!]! @belongsToMany(type: PAGINATOR) @guard
}
