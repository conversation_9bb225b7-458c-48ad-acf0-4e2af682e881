type Reel @model(class: "App\\Models\\Reel") {
    id: ID! @cacheKey
    caption: String
    full_url: String!
    thumbnail: String
    approval_status: String!
    created_at: DateTime!
    updated_at: DateTime!
    creator: CanUploadReel @morphTo
}

extend type PartnerPlace {
    reels: [Reel!]! @belongsToMany(relation: "reelsByPlace", type: PA<PERSON>NATOR)
}

extend type Creator {
    reels: [Reel!]
        @morphMany(relation: "reels", type: PA<PERSON><PERSON>TOR)
        @where(key: "approval_status", value: "approved")
    #@cache(maxAge: 900)
}

union CanUploadReel = Creator | Admin
