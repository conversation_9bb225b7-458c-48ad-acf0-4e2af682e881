type Review {
    id: ID!
    user: User! @belongsTo
    deal: Deal! @belongsTo
    partnerLocation: PartnerLocation! @belongsTo(relation: "partnerLocation")
    rating: Int!
    savings_amount: Float
    would_visit_again: Boolean!
    review_text: String
    created_at: DateTime!
    updated_at: DateTime!
}

type ReviewConnection {
    data: [Review!]!
    paginatorInfo: PaginatorInfo!
}

input SubmitReviewInput {
    deal_id: ID! @rules(apply: ["required", "exists:deals,id"])
    rating: Int! @rules(apply: ["required", "integer", "min:1", "max:5"])
    savings_amount: Float @rules(apply: ["nullable", "numeric", "min:0"])
    would_visit_again: Boolean! @rules(apply: ["required", "boolean"])
    review_text: String @rules(apply: ["nullable", "string", "max:1000"])
}

input UpdateReviewInput {
    id: ID! @rules(apply: ["required", "exists:reviews,id"])
    rating: Int @rules(apply: ["nullable", "integer", "min:1", "max:5"])
    savings_amount: Float @rules(apply: ["nullable", "numeric", "min:0"])
    would_visit_again: <PERSON>olean @rules(apply: ["nullable", "boolean"])
    review_text: String @rules(apply: ["nullable", "string", "max:1000"])
}

type SubmitReviewResponse {
    review: Review!
    message: String!
}

type UpdateReviewResponse {
    review: Review!
    message: String!
}

enum ReviewSortBy {
    CREATED_AT
    RATING
    SAVINGS_AMOUNT
}

enum SortOrder {
    ASC
    DESC
}

input ReviewFilters {
    rating: Int @rules(apply: ["nullable", "integer", "min:1", "max:5"])
    min_rating: Int @rules(apply: ["nullable", "integer", "min:1", "max:5"])
    would_visit_again: Boolean @rules(apply: ["nullable", "boolean"])
    has_review_text: Boolean @rules(apply: ["nullable", "boolean"])
}
