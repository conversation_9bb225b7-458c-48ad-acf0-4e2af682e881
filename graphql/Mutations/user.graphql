extend type Mutation {
    socialLogin(
        provider: SocialLoginProvider!
        token: String!
        firstName: String
        lastName: String
    ): SocialLoginResponse!
        @field(resolver: "App\\GraphQL\\Mutations\\User\\SocialLoginMutation")
}

extend type Mutation @guard {
    logout: LogoutResponse!
        @field(resolver: "App\\GraphQL\\Mutations\\User\\LogoutMutation")

    updateProfile(input: UpdateProfileInput! @spread): UpdateProfileResponse
        @field(resolver: "App\\GraphQL\\Mutations\\User\\UpdateProfileMutation")

    followCreator(input: CreatorFollowInput!): CreatorFollowPayload!
        @field(resolver: "App\\GraphQL\\Mutations\\User\\FollowCreatorMutation")

    followPlace(input: PlaceFollowInput!): PlaceFollowPayload!
        @field(resolver: "App\\GraphQL\\Mutations\\User\\FollowPlaceMutation")

    like(input: LikeInput!): LikePayload!
        @field(resolver: "App\\GraphQL\\Mutations\\User\\LikeMutation")
}

#Profile
input UpdateProfileInput {
    name: String!
}

type UpdateProfileResponse {
    status: Boolean!
    message: String!
    me: User!
}

input CreatorFollowInput {
    id: ID! @rules(apply: ["exists:\\App\\Models\\Creator,id"])
}

type CreatorFollowPayload {
    status: Boolean!
    message: String!
    creator: Creator!
}

input PlaceFollowInput {
    id: ID! @rules(apply: ["exists:\\App\\Models\\PartnerLocation,id"])
}

type PlaceFollowPayload {
    status: Boolean!
    message: String!
    place: PartnerPlace!
}

input LikeInput {
    id: ID!
    type: LikableType!
}

type LikePayload {
    status: Boolean!
    message: String!
    likeable: Likable
}

enum LikableType {
    PARTNER_PLACE
    CREATOR
    REEL
}
