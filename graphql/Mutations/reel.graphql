extend type Mutation @guard {
    getReelUploadUrl(input: GetReelUploadUrlInput!): GetReelUploadUrlResponse!
        @field(
            resolver: "App\\GraphQL\\Mutations\\Reel\\GetReelUploadUrlMutation"
        )

    createReel(input: CreateReelInput!): CreateReelResponse!
        @field(resolver: "App\\GraphQL\\Mutations\\Reel\\CreateReelMutation")
}

input CreateReelInput {
    caption: String
    url: String! @rules(apply: ["required", "string"])
    thumbnail: String
    partner_location_id: ID @rules(apply: ["exists:partner_locations,id"])
}

input GetReelUploadUrlInput {
    filename: String! @rules(apply: ["required", "string"])
    contentType: String! @rules(apply: ["required", "string"])
}

type GetReelUploadUrlResponse {
    path: String!
    url: String!
    headers: [Header!]!
}

type Header {
    key: String!
    value: String!
}

type CreateReelResponse {
    reel: Reel!
}
