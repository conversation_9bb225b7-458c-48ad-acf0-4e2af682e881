extend type Query {
    deal(
        id: ID! @rules(apply: ["exists:\\App\\Models\\Deal,id"]) @whereKey
    ): Deal! @find

    deals: [Deal!]! @paginate #@cache(maxAge: 60)
}

extend type Query @guard {
    """
    MyDeal will be cached until it updates from
    Your side
    """
    myDeal(
        id: ID! @rules(apply: ["exists:\\App\\Models\\UserDeal,id"]) @whereKey
    ): MyDeal!
        @whereAuth(relation: "user")
        @find(model: "App\\Models\\UserDeal")

    myDeals(orderBy: [OrderByClause!] @orderBy): [MyDeal!]
        @paginate(builder: "App\\GraphQL\\Types\\Deal\\MyDeal")
        @whereAuth(relation: "user")
}
