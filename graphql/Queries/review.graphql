extend type Query @guard {
    reviewsByPlace(
        partner_location_id: ID!
            @rules(apply: ["required", "exists:partner_locations,id"])
        filters: ReviewFilters
        sort_by: ReviewSortBy = CREATED_AT
        sort_order: SortOrder = DESC
    ): [Review!]!
        @paginate(builder: "App\\GraphQL\\Queries\\Review\\ReviewsByPlaceQuery")

    reviewsByUser(
        user_id: ID @rules(apply: ["nullable", "exists:users,id"])
        filters: ReviewFilters
        sort_by: ReviewSortBy = CREATED_AT
        sort_order: SortOrder = DESC
    ): [Review!]!
        @paginate(builder: "App\\GraphQL\\Queries\\Review\\ReviewsByUserQuery")

    reviewsByDeal(
        deal_id: ID! @rules(apply: ["required", "exists:deals,id"])
        filters: ReviewFilters
        sort_by: ReviewSortBy = CREATED_AT
        sort_order: SortOrder = DESC
    ): [Review!]!
        @paginate(builder: "App\\GraphQL\\Queries\\Review\\ReviewsByDealQuery")

    userReviewForDeal(
        deal_id: ID! @rules(apply: ["required", "exists:deals,id"])
    ): Review
        @field(
            resolver: "App\\GraphQL\\Queries\\Review\\UserReviewForDealQuery"
        )
}
