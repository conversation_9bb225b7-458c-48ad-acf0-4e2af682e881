<?php

namespace Tests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Nuwave\Lighthouse\Testing\MakesGraphQLRequests;
use Nuwave\Lighthouse\Testing\RefreshesSchemaCache;
use Nuwave\Lighthouse\Testing\TestsSubscriptions;

abstract class TestCase extends BaseTestCase
{
    use MakesGraphQLRequests;
    use RefreshDatabase;
    use RefreshesSchemaCache;
    use TestsSubscriptions;

    public bool $seed = true;

    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub

        app(\Nuwave\Lighthouse\Schema\SchemaBuilder::class)->schema();
    }
}
