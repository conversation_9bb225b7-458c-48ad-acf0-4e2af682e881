<?php

use App\Actions\Review\UpdateReviewAction;
use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use GraphQL\Error\UserError;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(Tests\TestCase::class, RefreshDatabase::class);

describe('UpdateReviewAction', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();
        $this->partnerLocation = PartnerLocation::factory()->create();
        $this->deal = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);

        $this->review = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
            'savings_amount' => 15.00,
            'would_visit_again' => false,
            'review_text' => 'Original review',
        ]);
    });

    it('can update all review fields', function () {
        $data = [
            'id' => $this->review->id,
            'rating' => 5,
            'savings_amount' => 30.00,
            'would_visit_again' => true,
            'review_text' => 'Updated review text',
        ];

        $updatedReview = UpdateReviewAction::handle($this->user, $data);

        expect($updatedReview->rating)->toBe(5)
            ->and($updatedReview->savings_amount)->toBe('30.00')
            ->and($updatedReview->would_visit_again)->toBeTrue()
            ->and($updatedReview->review_text)->toBe('Updated review text');
    });

    it('can update only specific fields', function () {
        $data = [
            'id' => $this->review->id,
            'rating' => 4,
        ];

        $updatedReview = UpdateReviewAction::handle($this->user, $data);

        expect($updatedReview->rating)->toBe(4)
            ->and($updatedReview->savings_amount)->toBe('15.00') // unchanged
            ->and($updatedReview->would_visit_again)->toBeFalse() // unchanged
            ->and($updatedReview->review_text)->toBe('Original review'); // unchanged
    });

    it('can set fields to null', function () {
        $data = [
            'id' => $this->review->id,
            'savings_amount' => null,
            'review_text' => null,
        ];

        $updatedReview = UpdateReviewAction::handle($this->user, $data);

        expect($updatedReview->savings_amount)->toBeNull()
            ->and($updatedReview->review_text)->toBeNull()
            ->and($updatedReview->rating)->toBe(3) // unchanged
            ->and($updatedReview->would_visit_again)->toBeFalse(); // unchanged
    });

    it('throws error if user does not own the review', function () {
        $data = [
            'id' => $this->review->id,
            'rating' => 5,
        ];

        expect(fn () => UpdateReviewAction::handle($this->otherUser, $data))
            ->toThrow(UserError::class, 'You can only update your own reviews.');
    });

    it('updates partner location rating when rating is changed', function () {
        $data = [
            'id' => $this->review->id,
            'rating' => 5,
        ];

        $initialRating = $this->partnerLocation->rating;

        UpdateReviewAction::handle($this->user, $data);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->not->toBe($initialRating);
    });

    it('does not update partner location rating when rating is not changed', function () {
        $data = [
            'id' => $this->review->id,
            'review_text' => 'New text only',
        ];

        $initialRating = $this->partnerLocation->rating;

        UpdateReviewAction::handle($this->user, $data);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe($initialRating);
    });

    it('returns fresh model instance', function () {
        $data = [
            'id' => $this->review->id,
            'rating' => 5,
        ];

        $updatedReview = UpdateReviewAction::handle($this->user, $data);

        expect($updatedReview->id)->toBe($this->review->id)
            ->and($updatedReview)->not->toBe($this->review); // Different instance
    });

    it('updates review in database transaction', function () {
        $data = [
            'id' => $this->review->id,
            'rating' => 5,
            'review_text' => 'Updated in transaction',
        ];

        UpdateReviewAction::handle($this->user, $data);

        $this->review->refresh();
        expect($this->review->rating)->toBe(5)
            ->and($this->review->review_text)->toBe('Updated in transaction');
    });

    it('throws error for non-existent review', function () {
        $data = [
            'id' => 99999,
            'rating' => 5,
        ];

        expect(fn () => UpdateReviewAction::handle($this->user, $data))
            ->toThrow(Exception::class);
    });
});
