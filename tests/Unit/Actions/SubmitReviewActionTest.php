<?php

use App\Actions\Review\SubmitReviewAction;
use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use App\Models\UserDeal;
use GraphQL\Error\UserError;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(Tests\TestCase::class, RefreshDatabase::class);

describe('SubmitReviewAction', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->partnerLocation = PartnerLocation::factory()->create();
        $this->deal = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);

        // Create a redeemed user deal
        $this->userDeal = UserDeal::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'redeemed_at' => now(),
        ]);
    });

    it('can submit a review for a redeemed deal', function () {
        $data = [
            'deal_id' => $this->deal->id,
            'rating' => 5,
            'savings_amount' => 25.50,
            'would_visit_again' => true,
            'review_text' => 'Great experience!',
        ];

        $review = SubmitReviewAction::handle($this->user, $data);

        expect($review)->toBeInstanceOf(Review::class)
            ->and($review->user_id)->toBe($this->user->id)
            ->and($review->deal_id)->toBe($this->deal->id)
            ->and($review->partner_location_id)->toBe($this->partnerLocation->id)
            ->and($review->rating)->toBe(5)
            ->and($review->savings_amount)->toBe('25.50')
            ->and($review->would_visit_again)->toBeTrue()
            ->and($review->review_text)->toBe('Great experience!');
    });

    it('can submit a review without optional fields', function () {
        $data = [
            'deal_id' => $this->deal->id,
            'rating' => 4,
            'would_visit_again' => false,
        ];

        $review = SubmitReviewAction::handle($this->user, $data);

        expect($review)->toBeInstanceOf(Review::class)
            ->and($review->rating)->toBe(4)
            ->and($review->savings_amount)->toBeNull()
            ->and($review->would_visit_again)->toBeFalse()
            ->and($review->review_text)->toBeNull();
    });

    it('throws error if user has not redeemed the deal', function () {
        $unredeemed = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);

        $data = [
            'deal_id' => $unredeemed->id,
            'rating' => 5,
            'would_visit_again' => true,
        ];

        expect(fn () => SubmitReviewAction::handle($this->user, $data))
            ->toThrow(UserError::class, 'You can only review deals that you have redeemed.');
    });

    it('throws error if user has already reviewed the deal', function () {
        // Create existing review
        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
        ]);

        $data = [
            'deal_id' => $this->deal->id,
            'rating' => 5,
            'would_visit_again' => true,
        ];

        expect(fn () => SubmitReviewAction::handle($this->user, $data))
            ->toThrow(UserError::class, 'You have already reviewed this deal.');
    });

    it('updates partner location rating after submitting review', function () {
        $data = [
            'deal_id' => $this->deal->id,
            'rating' => 5,
            'would_visit_again' => true,
        ];

        $initialRating = $this->partnerLocation->rating;

        SubmitReviewAction::handle($this->user, $data);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->not->toBe($initialRating);
    });

    it('creates review in database transaction', function () {
        $data = [
            'deal_id' => $this->deal->id,
            'rating' => 5,
            'would_visit_again' => true,
        ];

        $initialCount = Review::count();

        SubmitReviewAction::handle($this->user, $data);

        expect(Review::count())->toBe($initialCount + 1);
    });
});
