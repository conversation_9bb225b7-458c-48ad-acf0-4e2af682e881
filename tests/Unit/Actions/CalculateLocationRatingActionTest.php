<?php

use App\Actions\Review\CalculateLocationRatingAction;
use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(Tests\TestCase::class, RefreshDatabase::class);

describe('CalculateLocationRatingAction', function () {
    beforeEach(function () {
        $this->partnerLocation = PartnerLocation::factory()->create([
            'rating' => null,
            'reviews_count' => 0,
        ]);
        $this->deal = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);
        $this->users = User::factory()->count(3)->create();
    });

    it('calculates average rating correctly', function () {
        // Create reviews with ratings 3, 4, 5 (average = 4.00)
        Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        Review::factory()->create([
            'user_id' => $this->users[1]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 4,
        ]);

        Review::factory()->create([
            'user_id' => $this->users[2]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        CalculateLocationRatingAction::handle($this->partnerLocation->id);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe('4.00')
            ->and($this->partnerLocation->reviews_count)->toBe(3);
    });

    it('rounds rating to 2 decimal places', function () {
        // Create reviews with ratings 1, 2, 5 (average = 2.666... -> 2.67)
        Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 1,
        ]);

        Review::factory()->create([
            'user_id' => $this->users[1]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 2,
        ]);

        Review::factory()->create([
            'user_id' => $this->users[2]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        CalculateLocationRatingAction::handle($this->partnerLocation->id);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe('2.67');
    });

    it('sets rating to null when no reviews exist', function () {
        CalculateLocationRatingAction::handle($this->partnerLocation->id);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBeNull()
            ->and($this->partnerLocation->reviews_count)->toBe(0);
    });

    it('updates review count correctly', function () {
        Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        CalculateLocationRatingAction::handle($this->partnerLocation->id);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->reviews_count)->toBe(1);
    });

    it('only counts reviews for the specific location', function () {
        $otherLocation = PartnerLocation::factory()->create();
        $otherDeal = Deal::factory()->create(['partner_location_id' => $otherLocation->id]);

        // Review for our location
        Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        // Review for other location
        Review::factory()->create([
            'user_id' => $this->users[1]->id,
            'deal_id' => $otherDeal->id,
            'partner_location_id' => $otherLocation->id,
            'rating' => 1,
        ]);

        CalculateLocationRatingAction::handle($this->partnerLocation->id);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe('5.00')
            ->and($this->partnerLocation->reviews_count)->toBe(1);
    });

    it('handles single review correctly', function () {
        Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        CalculateLocationRatingAction::handle($this->partnerLocation->id);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe('3.00')
            ->and($this->partnerLocation->reviews_count)->toBe(1);
    });

    it('throws error for non-existent partner location', function () {
        expect(fn () => CalculateLocationRatingAction::handle(99999))
            ->toThrow(Exception::class);
    });

    it('recalculates when reviews are updated', function () {
        $review = Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        CalculateLocationRatingAction::handle($this->partnerLocation->id);
        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe('3.00');

        // Update the review rating
        $review->update(['rating' => 5]);

        CalculateLocationRatingAction::handle($this->partnerLocation->id);
        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe('5.00');
    });
});
