<?php

use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(Tests\TestCase::class, RefreshDatabase::class);

describe('Review Model', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->partnerLocation = PartnerLocation::factory()->create();
        $this->deal = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);
    });

    it('can create a review', function () {
        $review = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
        ]);

        expect($review)->toBeInstanceOf(Review::class)
            ->and($review->user_id)->toBe($this->user->id)
            ->and($review->deal_id)->toBe($this->deal->id)
            ->and($review->partner_location_id)->toBe($this->partnerLocation->id);
    });

    it('belongs to a user', function () {
        $review = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
        ]);

        expect($review->user)->toBeInstanceOf(User::class)
            ->and($review->user->id)->toBe($this->user->id);
    });

    it('belongs to a deal', function () {
        $review = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
        ]);

        expect($review->deal)->toBeInstanceOf(Deal::class)
            ->and($review->deal->id)->toBe($this->deal->id);
    });

    it('belongs to a partner location', function () {
        $review = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
        ]);

        expect($review->partnerLocation)->toBeInstanceOf(PartnerLocation::class)
            ->and($review->partnerLocation->id)->toBe($this->partnerLocation->id);
    });

    it('casts savings_amount to decimal', function () {
        $review = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'savings_amount' => '25.50',
        ]);

        expect($review->savings_amount)->toBe('25.50'); // decimal:2 cast returns string
    });

    it('casts would_visit_again to boolean', function () {
        $review = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'would_visit_again' => 1,
        ]);

        expect($review->would_visit_again)->toBeBool()
            ->and($review->would_visit_again)->toBeTrue();
    });

    it('has withRating scope', function () {
        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id])->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        $fiveStarReviews = Review::withRating(5)->get();
        expect($fiveStarReviews)->toHaveCount(1)
            ->and($fiveStarReviews->first()->rating)->toBe(5);
    });

    it('has withMinRating scope', function () {
        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id])->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        $highRatedReviews = Review::withMinRating(4)->get();
        expect($highRatedReviews)->toHaveCount(1)
            ->and($highRatedReviews->first()->rating)->toBe(5);
    });

    it('has wouldVisitAgain scope', function () {
        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'would_visit_again' => true,
        ]);

        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id])->id,
            'partner_location_id' => $this->partnerLocation->id,
            'would_visit_again' => false,
        ]);

        $positiveReviews = Review::wouldVisitAgain()->get();
        expect($positiveReviews)->toHaveCount(1)
            ->and($positiveReviews->first()->would_visit_again)->toBeTrue();
    });

    it('has recent scope', function () {
        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'created_at' => now()->subDays(10),
        ]);

        $recentReview = Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id])->id,
            'partner_location_id' => $this->partnerLocation->id,
            'created_at' => now()->subDays(5),
        ]);

        $recentReviews = Review::recent()->get();
        expect($recentReviews)->toHaveCount(2)
            ->and($recentReviews->first()->id)->toBe($recentReview->id); // Most recent first
    });

    it('has highestRated scope', function () {
        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id])->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        $reviews = Review::highestRated()->get();
        expect($reviews->first()->rating)->toBe(5)
            ->and($reviews->last()->rating)->toBe(3);
    });

    it('enforces unique constraint on user and deal', function () {
        Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
        ]);

        expect(fn () => Review::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
        ]))->toThrow(Exception::class);
    });

    // Note: Database constraint validation tests removed since we're using model validation instead
});
