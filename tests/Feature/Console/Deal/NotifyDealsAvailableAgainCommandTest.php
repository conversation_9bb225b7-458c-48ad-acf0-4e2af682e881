<?php

use App\Console\Commands\Deal\NotifyDealsAvailableAgainCommand;
use App\Models\Deal;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\User;
use App\Models\UserDeal;
use App\Notifications\DealIsReservableNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\artisan;

beforeEach(function () {
    Notification::fake();
});

test('it sends notifications for deals available again', function () {
    // Set the current date
    Carbon::setTestNow(Carbon::parse('2023-06-01 12:00'));

    // Create a partner, partner location, and deal
    /** @var Partner $partner */
    $partner = Partner::factory()->create(['name' => 'Test Partner']);
    /** @var PartnerLocation $partnerLocation */
    $partnerLocation = PartnerLocation::factory()->create([
        'name' => 'Test Location',
        'partner_id' => $partner->id,
    ]);
    /** @var Deal $deal */
    $deal = Deal::factory()->create([
        'partner_location_id' => $partnerLocation->id,
        'title' => 'Test Deal',
    ]);

    // Create users with and without FCM tokens
    /** @var User $userWithToken */
    $userWithToken = User::factory()->create(['fcm_token' => 'test-token-1']);
    /** @var User $userWithoutToken */
    $userWithoutToken = User::factory()->create(['fcm_token' => null]);

    // Create user deals with reuse_after set to yesterday
    /** @var UserDeal $userDealWithToken */
    $userDealWithToken = UserDeal::factory()->create([
        'user_id' => $userWithToken->id,
        'deal_id' => $deal->id,
        'status' => 'redeemed',
        'reuse_after' => Carbon::yesterday()->toDateString(),
    ]);

    /** @var UserDeal $userDealWithoutToken */
    $userDealWithoutToken = UserDeal::factory()->create([
        'user_id' => $userWithoutToken->id,
        'deal_id' => $deal->id,
        'status' => 'redeemed',
        'reuse_after' => Carbon::yesterday()->toDateString(),
    ]);

    // Create a user deal with reuse_after not set to yesterday
    /** @var User $anotherUser */
    $anotherUser = User::factory()->create(['fcm_token' => 'test-token-2']);
    /** @var UserDeal $anotherUserDeal */
    $anotherUserDeal = UserDeal::factory()->create([
        'user_id' => $anotherUser->id,
        'deal_id' => $deal->id,
        'status' => 'redeemed',
        'reuse_after' => Carbon::today()->toDateString(),
    ]);

    // Run the command
    artisan(NotifyDealsAvailableAgainCommand::class);

    // Assert notifications were sent only to users with FCM tokens and reuse_after set to yesterday
    Notification::assertSentTo(
        [$userWithToken],
        DealIsReservableNotification::class,
        function (DealIsReservableNotification $notification, $channels, $notifiable) use ($userDealWithToken) {
            /** @var UserDeal $userDeal */
            $userDeal = $notification->getUserDeal();

            return $userDeal->id === $userDealWithToken->id;
        }
    );

    // Assert notifications were not sent to users without FCM tokens or with reuse_after not set to yesterday
    Notification::assertNotSentTo(
        [$userWithoutToken, $anotherUser],
        DealIsReservableNotification::class
    );
});

test('it does not send notifications for deleted deals', function () {
    // Set the current date
    Carbon::setTestNow(Carbon::parse('2023-06-01 12:00'));

    // Create a partner, partner location, and deal
    /** @var Partner $partner */
    $partner = Partner::factory()->create(['name' => 'Test Partner']);
    /** @var PartnerLocation $partnerLocation */
    $partnerLocation = PartnerLocation::factory()->create([
        'name' => 'Test Location',
        'partner_id' => $partner->id,
    ]);
    /** @var Deal $deal */
    $deal = Deal::factory()->create([
        'partner_location_id' => $partnerLocation->id,
        'title' => 'Test Deal',
    ]);

    // Create a user with FCM token
    /** @var User $user */
    $user = User::factory()->create(['fcm_token' => 'test-token']);

    // Create a user deal with reuse_after set to yesterday
    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::factory()->create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'status' => 'redeemed',
        'reuse_after' => Carbon::yesterday()->toDateString(),
    ]);

    // Delete the deal
    $deal->delete();

    // Run the command
    artisan(NotifyDealsAvailableAgainCommand::class);

    // Assert no notifications were sent
    Notification::assertNothingSent();
});
