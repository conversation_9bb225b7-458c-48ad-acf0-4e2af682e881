<?php

use App\Models\Deal;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\User;
use App\Models\UserDeal;
use App\Notifications\DealRedeemableNotification;
use Illuminate\Support\Facades\Notification;

test('notification is sent when deal becomes redeemable', function () {
    Notification::fake();

    /** @var User $user */
    $user = User::factory()->create();
    /** @var PartnerLocation $partnerLocation */
    $partnerLocation = PartnerLocation::factory()->create(['name' => 'Test Restaurant']);
    /** @var Deal $deal */
    $deal = Deal::factory()->create(['partner_location_id' => $partnerLocation->id]);

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::factory()->create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'status' => 'upcoming',
        'reserved_at' => now()->subHour(),
    ]);

    $user->notify(new DealRedeemableNotification($userDeal));

    Notification::assertSentTo(
        $user,
        DealRedeemableNotification::class,
        function ($notification, $channels) use ($userDeal) {
            $data = $notification->toArray($userDeal->user);
            // PHPStan fix: assert deal is Deal
            $deal = $userDeal->deal;
            if (! $deal instanceof \App\Models\Deal) {
                return false;
            }

            /** @var PartnerLocation $partnerLocation */
            $partnerLocation = $deal->partner_place;

            /** @var Partner $partner */
            $partner = $partnerLocation->partner;

            return $data['type'] === 'deal_redeemable'
                && $data['deal_id'] === $userDeal->deal_id
                && $data['user_deal_id'] === $userDeal->id
                && $data['partner_name'] === $partner->name
                && str_contains($data['deep_link'], (string) $userDeal->deal_id);
        }
    );
});

test('notification contains correct fcm message', function () {
    /** @var User $user */
    $user = User::factory()->create();
    /** @var PartnerLocation $partnerLocation */
    $partnerLocation = PartnerLocation::factory()->create(['name' => 'Test Restaurant']);

    /** @var \App\Models\Partner $partner */
    $partner = $partnerLocation->partner;

    /** @var Deal $deal */
    $deal = Deal::factory()->create(['partner_location_id' => $partnerLocation->id]);

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::factory()->create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'status' => 'upcoming',
        'reserved_at' => now()->subHour(),
    ]);

    $notification = new DealRedeemableNotification($userDeal);
    $fcmMessage = $notification->toFcm($user);

    // Check data payload
    expect($fcmMessage->data)
        ->toHaveKey('type', 'deal_redeemable')
        ->toHaveKey('deal_id', (string) $deal->id)
        ->toHaveKey('user_deal_id', (string) $userDeal->id)
        ->toHaveKey('partner_name', $partner->name)
        ->toHaveKey('deep_link', "conari://deals/{$deal->id}");

    // Check notification payload
    expect($fcmMessage->notification)
        ->toHaveKey('title', 'Your Deal is Now Active! 🎉')
        ->toHaveKey('body', "Great news! Your reserved deal at {$partner->name} - {$partnerLocation->name} is now active. Redeem it before it expires!");

    // Check Android configuration
    expect($fcmMessage->custom['android']['notification'])
        ->toHaveKey('click_action', 'FLUTTER_NOTIFICATION_CLICK')
        ->toHaveKey('color', '#4CAF50')
        ->toHaveKey('sound', 'notification')
        ->toHaveKey('icon', 'notification_icon')
        ->toHaveKey('channel_id', 'deals');
    expect($fcmMessage->custom['android']['fcm_options'])
        ->toHaveKey('analytics_label', 'deal_redeemable');

    // Check iOS configuration
    expect($fcmMessage->custom['apns']['payload']['aps'])
        ->toHaveKey('category', 'DEAL_REDEEMABLE')
        ->toHaveKey('sound', 'default');
    expect($fcmMessage->custom['apns']['fcm_options'])
        ->toHaveKey('analytics_label', 'deal_redeemable_ios');
});
