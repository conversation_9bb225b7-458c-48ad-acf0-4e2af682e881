<?php

use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use App\Models\UserDeal;
use Laravel\Sanctum\Sanctum;


describe('Review System Integration', function () {
    beforeEach(function () {
        $this->partnerLocation = PartnerLocation::factory()->create([
            'rating' => null,
            'reviews_count' => 0,
        ]);
        $this->deal = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);
        $this->users = User::factory()->count(5)->create();

        // Create redeemed user deals for all users
        foreach ($this->users as $user) {
            UserDeal::factory()->create([
                'user_id' => $user->id,
                'deal_id' => $this->deal->id,
                'redeemed_at' => now(),
            ]);
        }
    });

    it('calculates partner location rating correctly through complete workflow', function () {
        Sanctum::actingAs($this->users[0]);

        // Submit first review (rating: 5)
        $mutation = '
            mutation SubmitReview($input: SubmitReviewInput!) {
                submitReview(input: $input) {
                    review {
                        rating
                    }
                    message
                }
            }
        ';

        graphQL($mutation, [
            'input' => [
                'deal_id' => (string) $this->deal->id,
                'rating' => 5,
                'would_visit_again' => true,
            ],
        ]);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(5.00)
            ->and($this->partnerLocation->reviews_count)->toBe(1);

        // Submit second review (rating: 3)
        Sanctum::actingAs($this->users[1]);
        
        graphQL($mutation, [
            'input' => [
                'deal_id' => (string) $this->deal->id,
                'rating' => 3,
                'would_visit_again' => false,
            ],
        ]);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(4.00) // (5+3)/2
            ->and($this->partnerLocation->reviews_count)->toBe(2);

        // Submit third review (rating: 4)
        Sanctum::actingAs($this->users[2]);
        
        graphQL($mutation, [
            'input' => [
                'deal_id' => (string) $this->deal->id,
                'rating' => 4,
                'would_visit_again' => true,
            ],
        ]);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(4.00) // (5+3+4)/3
            ->and($this->partnerLocation->reviews_count)->toBe(3);
    });

    it('updates partner location rating when review is updated', function () {
        // Create initial review
        $review = Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(3.00);

        // Update review rating
        Sanctum::actingAs($this->users[0]);

        $updateMutation = '
            mutation UpdateReview($input: UpdateReviewInput!) {
                updateReview(input: $input) {
                    review {
                        rating
                    }
                    message
                }
            }
        ';

        graphQL($updateMutation, [
            'input' => [
                'id' => (string) $review->id,
                'rating' => 5,
            ],
        ]);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(5.00);
    });

    it('maintains accurate rating with multiple reviews and updates', function () {
        // Create multiple reviews
        $reviews = [];
        foreach ($this->users as $index => $user) {
            $reviews[] = Review::factory()->create([
                'user_id' => $user->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
                'rating' => $index + 1, // ratings: 1, 2, 3, 4, 5
            ]);
        }

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(3.00) // (1+2+3+4+5)/5
            ->and($this->partnerLocation->reviews_count)->toBe(5);

        // Update one review
        Sanctum::actingAs($this->users[0]);

        $updateMutation = '
            mutation UpdateReview($input: UpdateReviewInput!) {
                updateReview(input: $input) {
                    review {
                        rating
                    }
                    message
                }
            }
        ';

        graphQL($updateMutation, [
            'input' => [
                'id' => (string) $reviews[0]->id,
                'rating' => 5, // Change from 1 to 5
            ],
        ]);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(3.80) // (5+2+3+4+5)/5
            ->and($this->partnerLocation->reviews_count)->toBe(5);
    });

    it('handles review deletion correctly', function () {
        // Create reviews
        $review1 = Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
        ]);

        Review::factory()->create([
            'user_id' => $this->users[1]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 3,
        ]);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(4.00);

        // Soft delete one review
        $review1->delete();

        // Manually recalculate (in real app this would be triggered by model events)
        \App\Actions\Review\CalculateLocationRatingAction::handle($this->partnerLocation->id);

        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(3.00) // Only review2 remains
            ->and($this->partnerLocation->reviews_count)->toBe(1);
    });

    it('integrates with existing user deal redemption verification', function () {
        $user = $this->users[0];
        Sanctum::actingAs($user);

        // Try to review without redeeming
        $unredeemed = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);

        $mutation = '
            mutation SubmitReview($input: SubmitReviewInput!) {
                submitReview(input: $input) {
                    review {
                        id
                    }
                    message
                }
            }
        ';

        $response = graphQL($mutation, [
            'input' => [
                'deal_id' => (string) $unredeemed->id,
                'rating' => 5,
                'would_visit_again' => true,
            ],
        ]);

        $response->assertGraphQLErrorMessage('You can only review deals that you have redeemed.');

        // Verify no review was created
        expect(Review::where('deal_id', $unredeemed->id)->count())->toBe(0);

        // Verify partner location rating unchanged
        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBeNull();
    });

    it('prevents duplicate reviews for same user-deal combination', function () {
        $user = $this->users[0];
        Sanctum::actingAs($user);

        $mutation = '
            mutation SubmitReview($input: SubmitReviewInput!) {
                submitReview(input: $input) {
                    review {
                        id
                    }
                    message
                }
            }
        ';

        // Submit first review
        graphQL($mutation, [
            'input' => [
                'deal_id' => (string) $this->deal->id,
                'rating' => 5,
                'would_visit_again' => true,
            ],
        ]);

        expect(Review::count())->toBe(1);

        // Try to submit second review for same deal
        $response = graphQL($mutation, [
            'input' => [
                'deal_id' => (string) $this->deal->id,
                'rating' => 3,
                'would_visit_again' => false,
            ],
        ]);

        $response->assertGraphQLError('You have already reviewed this deal.');

        // Verify only one review exists
        expect(Review::count())->toBe(1);
    });

    it('supports comprehensive review queries with filters and sorting', function () {
        // Create diverse reviews
        Review::factory()->create([
            'user_id' => $this->users[0]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 5,
            'would_visit_again' => true,
            'review_text' => 'Excellent service!',
            'savings_amount' => 50.00,
        ]);

        Review::factory()->create([
            'user_id' => $this->users[1]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 2,
            'would_visit_again' => false,
            'review_text' => null,
            'savings_amount' => null,
        ]);

        Review::factory()->create([
            'user_id' => $this->users[2]->id,
            'deal_id' => $this->deal->id,
            'partner_location_id' => $this->partnerLocation->id,
            'rating' => 4,
            'would_visit_again' => true,
            'review_text' => 'Good value',
            'savings_amount' => 25.00,
        ]);

        Sanctum::actingAs($this->users[0]);

        // Test filtering by minimum rating
        $query = '
            query ReviewsByPlace($partner_location_id: ID!, $filters: ReviewFilters) {
                reviewsByPlace(partner_location_id: $partner_location_id, filters: $filters) {
                    data {
                        rating
                    }
                    paginatorInfo {
                        total
                    }
                }
            }
        ';

        $response = $this->postGraphQL($query, [
            'partner_location_id' => (string) $this->partnerLocation->id,
            'filters' => [
                'min_rating' => 4,
            ],
        ]);

        $response->assertJson([
            'data' => [
                'reviewsByPlace' => [
                    'paginatorInfo' => [
                        'total' => 2, // ratings 4 and 5
                    ],
                ],
            ],
        ]);

        // Test filtering by would_visit_again
        $response = $this->postGraphQL($query, [
            'partner_location_id' => (string) $this->partnerLocation->id,
            'filters' => [
                'would_visit_again' => true,
            ],
        ]);

        $response->assertJson([
            'data' => [
                'reviewsByPlace' => [
                    'paginatorInfo' => [
                        'total' => 2, // Two positive reviews
                    ],
                ],
            ],
        ]);

        // Test filtering by has_review_text
        $response = $this->postGraphQL($query, [
            'partner_location_id' => (string) $this->partnerLocation->id,
            'filters' => [
                'has_review_text' => true,
            ],
        ]);

        $response->assertJson([
            'data' => [
                'reviewsByPlace' => [
                    'paginatorInfo' => [
                        'total' => 2, // Two reviews with text
                    ],
                ],
            ],
        ]);
    });

    it('maintains data consistency across all operations', function () {
        $user = $this->users[0];
        Sanctum::actingAs($user);

        // Submit review
        $submitMutation = '
            mutation SubmitReview($input: SubmitReviewInput!) {
                submitReview(input: $input) {
                    review {
                        id
                        rating
                    }
                    message
                }
            }
        ';

        $submitResponse = $this->postGraphQL($submitMutation, [
            'input' => [
                'deal_id' => (string) $this->deal->id,
                'rating' => 4,
                'would_visit_again' => true,
                'review_text' => 'Great experience',
                'savings_amount' => 30.00,
            ],
        ]);

        $reviewId = $submitResponse->json('data.submitReview.review.id');

        // Verify review exists in database
        $review = Review::find($reviewId);
        expect($review)->not->toBeNull()
            ->and($review->rating)->toBe(4)
            ->and($review->review_text)->toBe('Great experience')
            ->and($review->savings_amount)->toBe(30.00);

        // Verify partner location rating updated
        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(4.00);

        // Query review back
        $query = '
            query UserReviewForDeal($deal_id: ID!) {
                userReviewForDeal(deal_id: $deal_id) {
                    id
                    rating
                    review_text
                    savings_amount
                }
            }
        ';

        $queryResponse = $this->postGraphQL($query, [
            'deal_id' => (string) $this->deal->id,
        ]);

        $queryResponse->assertJson([
            'data' => [
                'userReviewForDeal' => [
                    'id' => $reviewId,
                    'rating' => 4,
                    'review_text' => 'Great experience',
                    'savings_amount' => 30.00,
                ],
            ],
        ]);

        // Update review
        $updateMutation = '
            mutation UpdateReview($input: UpdateReviewInput!) {
                updateReview(input: $input) {
                    review {
                        rating
                        review_text
                    }
                    message
                }
            }
        ';

        $this->postGraphQL($updateMutation, [
            'input' => [
                'id' => $reviewId,
                'rating' => 5,
                'review_text' => 'Updated: Excellent experience',
            ],
        ]);

        // Verify update in database
        $review->refresh();
        expect($review->rating)->toBe(5)
            ->and($review->review_text)->toBe('Updated: Excellent experience');

        // Verify partner location rating updated
        $this->partnerLocation->refresh();
        expect($this->partnerLocation->rating)->toBe(5.00);
    });
});
