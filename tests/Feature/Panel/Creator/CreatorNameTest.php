<?php

use App\Filament\Resources\CreatorResource\Pages\CreateCreator;
use App\Models\Admin;
use App\Models\Creator;
use App\Models\User;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

it('creates a creator with only first name', function () {
    Creator::truncate();

    /** @var User $user */
    $user = User::factory()->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    livewire(CreateCreator::class)// @phpstan-ignore-line
        ->fillForm([
            'user_id' => $user->id,
            'name' => 'John', // Legacy field
            'first_name' => 'John',
            'bio' => 'test bio',
            'username' => 'johntest',
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    \Pest\Laravel\assertDatabaseHas(Creator::class, [
        'user_id' => $user->id,
        'first_name' => '<PERSON>',
        'last_name' => null,
    ]);

    /** @var Creator $creator */
    $creator = Creator::query()->where('user_id', $user->id)->first();

    // Verify formatted_name is just the first name when no last name is provided
    expect($creator->formatted_name)->toBe('John');
});

it('creates a creator with first and last name', function () {
    Creator::truncate();

    /** @var User $user */
    $user = User::factory()->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    livewire(CreateCreator::class)// @phpstan-ignore-line
        ->fillForm([
            'user_id' => $user->id,
            'name' => 'John Smith', // Legacy field
            'first_name' => 'John',
            'last_name' => 'Smith',
            'bio' => 'test bio',
            'username' => 'johnsmith',
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    \Pest\Laravel\assertDatabaseHas(Creator::class, [
        'user_id' => $user->id,
        'first_name' => 'John',
        'last_name' => 'Smith',
    ]);

    /** @var Creator $creator */
    $creator = Creator::where('user_id', $user->id)->first();

    // Verify formatted_name is first_name.l when last name is provided
    expect($creator->formatted_name)->toBe('John.S');
});

it('creates a creator respecting bio limits', function () {
    Creator::truncate();

    /** @var User $user */
    $user = User::factory()->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    livewire(CreateCreator::class)// @phpstan-ignore-line
        ->fillForm([
            'user_id' => $user->id,
            'name' => 'John Smith', // Legacy field
            'first_name' => 'John',
            'last_name' => 'Smith',
            'bio' => $bio = str()->random(100),
            'username' => 'johnsmith',
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    $bio = substr($bio, 0, 30);

    \Pest\Laravel\assertDatabaseHas(Creator::class, [
        'user_id' => $user->id,
        'first_name' => 'John',
        'last_name' => 'Smith',
        'bio' => $bio,
    ]);
});

it('bio validation with valid data', function () {
    $user = User::factory()->create();

    $data = [
        'user_id' => $user->id,
        'name' => 'Test Creator',
        'first_name' => 'Test',
        'last_name' => 'Creator',
        'username' => 'testcreator',
        'bio' => "This is a short bio\nWith two lines\nAnd 30 chars max per line",
    ];

    $rules = [
        'user_id' => ['required', 'exists:users,id'],
        'name' => ['required', 'string', 'max:255'],
        'first_name' => ['required', 'string', 'max:255'],
        'last_name' => ['nullable', 'string', 'max:255'],
        'username' => ['required', 'string', 'max:255', 'unique:creators'],
        'tiktok_url' => ['nullable', 'url', 'max:255'],
        'instagram_url' => ['nullable', 'url', 'max:255'],
        'bio' => [
            'required',
            'string',
            'max:80',
            function ($attribute, $value, $fail) {
                if (! $value) {
                    return;
                }

                $lines = explode("\n", $value);

                if (count($lines) > 3) {
                    $fail('The bio cannot exceed 3 lines.');

                    return;
                }

                foreach ($lines as $index => $line) {
                    if (strlen(trim($line)) > 30) {
                        $fail('Line '.($index + 1).' of the bio cannot exceed 30 characters.');

                        return;
                    }
                }
            },
        ],
    ];

    $validator = validator($data, $rules);

    expect($validator->passes())->toBeTrue();
});

it('bio validation exceeds 80 characters', function () {
    $user = User::factory()->create();

    $data = [
        'user_id' => $user->id,
        'name' => 'Test Creator',
        'first_name' => 'Test',
        'last_name' => 'Creator',
        'username' => 'testcreator',
        'bio' => 'This bio is way too long and exceeds the maximum allowed character limit of 80 characters which should fail validation',
    ];

    $rules = [
        'user_id' => ['required', 'exists:users,id'],
        'name' => ['required', 'string', 'max:255'],
        'first_name' => ['required', 'string', 'max:255'],
        'last_name' => ['nullable', 'string', 'max:255'],
        'username' => ['required', 'string', 'max:255', 'unique:creators'],
        'tiktok_url' => ['nullable', 'url', 'max:255'],
        'instagram_url' => ['nullable', 'url', 'max:255'],
        'bio' => [
            'required',
            'string',
            'max:80',
            function ($attribute, $value, $fail) {
                if (! $value) {
                    return;
                }

                $lines = explode("\n", $value);

                if (count($lines) > 3) {
                    $fail('The bio cannot exceed 3 lines.');

                    return;
                }

                foreach ($lines as $index => $line) {
                    if (strlen(trim($line)) > 30) {
                        $fail('Line '.($index + 1).' of the bio cannot exceed 30 characters.');

                        return;
                    }
                }
            },
        ],
    ];

    $validator = validator($data, $rules);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('bio'))->toBeTrue();
});

it('bio validation exceeds 3 lines', function () {
    $user = User::factory()->create();

    $data = [
        'user_id' => $user->id,
        'name' => 'Test Creator',
        'first_name' => 'Test',
        'last_name' => 'Creator',
        'username' => 'testcreator',
        'bio' => "Line 1\nLine 2\nLine 3\nLine 4",
    ];

    $rules = [
        'user_id' => ['required', 'exists:users,id'],
        'name' => ['required', 'string', 'max:255'],
        'first_name' => ['required', 'string', 'max:255'],
        'last_name' => ['nullable', 'string', 'max:255'],
        'username' => ['required', 'string', 'max:255', 'unique:creators'],
        'tiktok_url' => ['nullable', 'url', 'max:255'],
        'instagram_url' => ['nullable', 'url', 'max:255'],
        'bio' => [
            'required',
            'string',
            'max:80',
            function ($attribute, $value, $fail) {
                if (! $value) {
                    return;
                }

                $lines = explode("\n", $value);

                if (count($lines) > 3) {
                    $fail('The bio cannot exceed 3 lines.');

                    return;
                }

                foreach ($lines as $index => $line) {
                    if (strlen(trim($line)) > 30) {
                        $fail('Line '.($index + 1).' of the bio cannot exceed 30 characters.');

                        return;
                    }
                }
            },
        ],
    ];

    $validator = validator($data, $rules);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('bio'))->toBeTrue();
});

it('bio validation exceeds 30 characters per line', function () {
    $user = User::factory()->create();

    $data = [
        'user_id' => $user->id,
        'name' => 'Test Creator',
        'first_name' => 'Test',
        'last_name' => 'Creator',
        'username' => 'testcreator',
        'bio' => "This line is way too long and exceeds thirty characters\nLine 2\nLine 3",
    ];

    $rules = [
        'user_id' => ['required', 'exists:users,id'],
        'name' => ['required', 'string', 'max:255'],
        'first_name' => ['required', 'string', 'max:255'],
        'last_name' => ['nullable', 'string', 'max:255'],
        'username' => ['required', 'string', 'max:255', 'unique:creators'],
        'tiktok_url' => ['nullable', 'url', 'max:255'],
        'instagram_url' => ['nullable', 'url', 'max:255'],
        'bio' => [
            'required',
            'string',
            'max:80',
            function ($attribute, $value, $fail) {
                if (! $value) {
                    return;
                }

                $lines = explode("\n", $value);

                if (count($lines) > 3) {
                    $fail('The bio cannot exceed 3 lines.');

                    return;
                }

                foreach ($lines as $index => $line) {
                    if (strlen(trim($line)) > 30) {
                        $fail('Line '.($index + 1).' of the bio cannot exceed 30 characters.');

                        return;
                    }
                }
            },
        ],
    ];

    $validator = validator($data, $rules);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('bio'))->toBeTrue();
});

it('bio validation with empty bio', function () {
    $user = User::factory()->create();

    $data = [
        'user_id' => $user->id,
        'name' => 'Test Creator',
        'first_name' => 'Test',
        'last_name' => 'Creator',
        'username' => 'testcreator',
        'bio' => '',
    ];

    $rules = [
        'user_id' => ['required', 'exists:users,id'],
        'name' => ['required', 'string', 'max:255'],
        'first_name' => ['required', 'string', 'max:255'],
        'last_name' => ['nullable', 'string', 'max:255'],
        'username' => ['required', 'string', 'max:255', 'unique:creators'],
        'tiktok_url' => ['nullable', 'url', 'max:255'],
        'instagram_url' => ['nullable', 'url', 'max:255'],
        'bio' => [
            'required',
            'string',
            'max:80',
            function ($attribute, $value, $fail) {
                if (! $value) {
                    return;
                }

                $lines = explode("\n", $value);

                if (count($lines) > 3) {
                    $fail('The bio cannot exceed 3 lines.');

                    return;
                }

                foreach ($lines as $index => $line) {
                    if (strlen(trim($line)) > 30) {
                        $fail('Line '.($index + 1).' of the bio cannot exceed 30 characters.');

                        return;
                    }
                }
            },
        ],
    ];

    $validator = validator($data, $rules);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('bio'))->toBeTrue();
});
