<?php

use App\Models\Creator;

it('returns all name fields for a creator', function () {
    Creator::truncate();

    // Create a creator with first and last name
    /** @var Creator $creator */
    $creator = Creator::factory()->create([
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
    ]);

    $response = graphQL(/** @lang GraphQL */ '
        query ($id: ID!) {
            creator(id: $id) {
                id
                name
                first_name
                last_name
                formatted_name
            }
        }
    ', [
        'id' => $creator->id,
    ]);

    // Assert that the response has the expected structure
    $response->assertJsonStructure([
        'data' => [
            'creator' => [
                'id',
                'name',
                'first_name',
                'last_name',
                'formatted_name',
            ],
        ],
    ]);

    // Get the creator data from the response
    $creatorData = $response->json('data.creator');

    // Assert that the first_name and last_name match what we set
    expect($creatorData['first_name'])->toBe('John');
    expect($creatorData['last_name'])->toBe('<PERSON>');

    // Assert that the formatted_name follows the expected format: first_name.l
    expect($creatorData['formatted_name'])->toBe('John.S');
});

it('returns correct formatted_name when only first name is provided', function () {
    Creator::truncate();

    // Create a creator with only first name
    /** @var Creator $creator */
    $creator = Creator::factory()->firstNameOnly()->create([
        'first_name' => 'John',
    ]);

    $response = graphQL(/** @lang GraphQL */ '
        query ($id: ID!) {
            creator(id: $id) {
                id
                name
                first_name
                last_name
                formatted_name
            }
        }
    ', [
        'id' => $creator->id,
    ]);

    // Assert that the response has the expected structure
    $response->assertJsonStructure([
        'data' => [
            'creator' => [
                'id',
                'name',
                'first_name',
                'last_name',
                'formatted_name',
            ],
        ],
    ]);

    // Get the creator data from the response
    $creatorData = $response->json('data.creator');

    // Assert that the first_name matches what we set and last_name is null
    expect($creatorData['first_name'])->toBe('John');
    expect($creatorData['last_name'])->toBeNull();

    // Assert that the formatted_name is just the first_name when no last_name is provided
    expect($creatorData['formatted_name'])->toBe('John');
});
