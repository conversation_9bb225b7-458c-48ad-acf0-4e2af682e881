<?php

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\Tag;
use App\Models\User;
use App\Models\UserDeal;
use Illuminate\Support\Collection;

use function Pest\Laravel\actingAs;

test('can query deal snapshot through GraphQL', function () {

    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');
    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state(fn (array $attributes) => [
            'from' => '14:00',
            'to' => '18:00',
            'day' => now()->format('w'),
        ]))
        ->create([
            'title' => 'Test Deal',
            'description' => 'Test Description',
            'max_saving' => 50.00,
            'max_usage_per_day' => 10,
            'reuse_limit_days' => 7,
        ]);

    /** @var Collection<int, Tag> $tags */
    $tags = Tag::factory()->category()->count(2)->create();
    $deal->service_options()->sync($tags->pluck('id'));

    $deal = $deal->refresh();

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserved_at' => now(),
        'status' => 'upcoming',
        'reserve_slot' => [
            'date' => now()->addDay()->format('Y-m-d'),
            'slot' => [
                'from' => '14:00',
                'to' => '18:00',
            ],
        ],
        'reuse_after' => now()->addDays(7),
    ]);

    /** @var \App\Enums\DealType $dealType */
    $dealType = $deal->deal_type;

    /** @var Collection<?Tag> $serviceTypes */
    $serviceTypes = $deal->service_options;

    // Manually set the deal snapshot
    $userDeal->deal_snapshot = [
        'title' => $deal->title,
        'description' => $deal->description,
        'deal_type' => $dealType->name,
        'service_options' => $serviceTypes->map(function (Tag $tag): array {
            return [
                'id' => $tag->id,
                'title' => $tag->title,
            ];
        })->toArray(),
        'max_saving' => $deal->max_saving,
        'max_usage_per_day' => $deal->max_usage_per_day,
        'reuse_limit_days' => $deal->reuse_limit_days,
    ];
    $userDeal->save();

    $response = graphQL(/** @lang GraphQL */ '
        query {
            myDeals(first: 10) {
                data {
                    id
                    status
                    deal_snapshot {
                        title
                        description
                        deal_type
                        max_saving
                        max_usage_per_day
                        reuse_limit_days
                        service_options {
                            id
                            title
                        }
                    }
                }
            }
        }
    ');

    $response->assertJson([
        'data' => [
            'myDeals' => [
                'data' => [
                    [
                        'id' => (string) $userDeal->id,
                        'status' => 'UPCOMING',
                        'deal_snapshot' => [
                            'title' => 'Test Deal',
                            'description' => 'Test Description',
                            'deal_type' => $dealType->name,
                            'max_saving' => 50.00,
                            'max_usage_per_day' => 10,
                            'reuse_limit_days' => 7,
                            'service_options' => [
                                [
                                    'id' => (string) $tags[0]->id,
                                    'title' => $tags[0]->title,
                                ],
                                [
                                    'id' => (string) $tags[1]->id,
                                    'title' => $tags[1]->title,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ]);
});

test('deal snapshot remains unchanged in GraphQL when deal is updated', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');
    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state(fn (array $attributes) => [
            'from' => '14:00',
            'to' => '18:00',
            'day' => now()->addDay()->format('w'),
        ]))
        ->create([
            'title' => 'Original Title',
            'description' => 'Original Description',
            'max_usage_per_day' => 10,
        ]);

    /** @var User $user */
    $user = User::factory()->create();

    actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal(
                    input: {id: $id, reserve_slot: {date: $date, slot: {from: $from, to: $to}}, myDealIdToRenew: null}
                ) {
                    myDeal {
                        id
                        status
                    }
                }
            }',
        [
            'id' => $deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 14:00'),
            'to' => now()->addDay()->format('Y-m-d 18:00'),
        ])->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id' => UserDeal::latest('id')->value('id'),
                        'status' => 'UPCOMING',
                    ],
                ],
            ],
        ]);

    // Update the deal
    $deal->update([
        'title' => 'Updated Title',
        'description' => 'Updated Description',
    ]);

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::query()->latest('id')->first();

    $response = graphQL(/** @lang GraphQL */ '
        query {
            myDeals(first: 10) {
                data {
                    id
                    deal_snapshot {
                        title
                        description
                    }
                }
            }
        }
    ');

    $response->assertJson([
        'data' => [
            'myDeals' => [
                'data' => [
                    [
                        'id' => (string) $userDeal->id,
                        'deal_snapshot' => [
                            'title' => 'Original Title',
                            'description' => 'Original Description',
                        ],
                    ],
                ],
            ],
        ],
    ]);
});
