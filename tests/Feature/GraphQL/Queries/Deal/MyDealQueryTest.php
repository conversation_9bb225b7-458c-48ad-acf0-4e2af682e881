<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\UserDeal;
use Laravel\Sanctum\Sanctum;

describe('MyDealQuery', function () {
    test('MyDealQuery', function () {

        /** @var User */
        $user = Sanctum::actingAs(User::factory()->create());

        $graphqlResponse = graphQL(/** @lang GraphQL */ '
            {
                myDeal(id: "1") {
                    data { id }
                }
            }
        ');

        expect($graphqlResponse)->toBeObject();
    });

    test('MyDealsQuery', function () {

        /** @var UserDeal $userDeal */
        $userDeal = UserDeal::factory()->create();

        /** @var User $user */
        $user = $userDeal->user;

        \Pest\Laravel\actingAs($user, 'sanctum');

        $graphqlResponse = graphQL(/** @lang GraphQL */ '
            {
                myDeals(first: 10) {
                    data { id }
                }
            }
        ')->assertJson([
            'data' => [
                'myDeals' => [
                    'data' => [
                        [
                            'id' => $userDeal->deal_id,
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('MyDealsQuery With default order reserve_slot.date ASC', function () {
        /** @var User $user */
        $user = User::factory()->create();

        /** @var UserDeal $userDeal */
        $userDeal = UserDeal::factory()->create([
            'user_id' => $user->id,
            'reserve_slot' => [
                'date' => now()->toDateString(),
                'slot' => [
                    'from' => '13:00',
                    'to' => '15:00',
                ],
            ],
        ]);

        /** @var UserDeal $userDeal2 */
        $userDeal2 = UserDeal::factory()->create([
            'user_id' => $user->id,
            'reserve_slot' => [
                'date' => now()->addDays(5)->toDateString(),
                'slot' => [
                    'from' => '13:00',
                    'to' => '15:00',
                ],
            ],
        ]);

        /** @var UserDeal $userDeal3 */
        $userDeal3 = UserDeal::factory()->create([
            'user_id' => $user->id,
            'reserve_slot' => [
                'date' => now()->addDays(2)->toDateString(),
                'slot' => [
                    'from' => '13:00',
                    'to' => '15:00',
                ],
            ],
        ]);

        \Pest\Laravel\actingAs($user, 'sanctum');

        $graphqlResponse = graphQL(/** @lang GraphQL */ '
            {
                myDeals(first: 10) {
                    data {
                       id
                    }
                }
            }
        ')->assertJson([
            'data' => [
                'myDeals' => [
                    'data' => [
                        [
                            'id' => $userDeal->id,
                        ],
                        [
                            'id' => $userDeal3->id,
                        ],
                        [
                            'id' => $userDeal2->id,
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('MyDealsQuery from deal object and expect only related records', function () {

        /** @var UserDeal $userDeal */
        $userDeal = UserDeal::factory()->create();

        /** @var User $user */
        $user = $userDeal->user;

        \Pest\Laravel\actingAs($user, 'sanctum');

        UserDeal::factory()->create([
            'user_id' => $user->id,
        ]);

        $graphqlResponse = graphQL(/** @lang GraphQL */ '
            {
               deals(first: 2) {
                   data {
                       id
                       title
                       myDeals(first: 10) {
                        data {
                            id
                        }
                      }
                   }
               }
            }
        ');

        $graphqlResponse->assertJson([
            'data' => [
                'deals' => [
                    'data' => [
                        [
                            'id' => $userDeal->deal_id,
                            'myDeals' => [
                                'data' => [
                                    [
                                        'id' => $userDeal->id,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $graphqlResponse->assertJsonCount(1, 'data.deals.data.0.myDeals');
    });
});
