<?php

use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(Tests\TestCase::class, RefreshDatabase::class);

describe('Review GraphQL Queries', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->partnerLocation = PartnerLocation::factory()->create();
        $this->deal = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);
        
        Sanctum::actingAs($this->user);
    });

    describe('reviewsByPlace query', function () {
        beforeEach(function () {
            $this->users = User::factory()->count(3)->create();
            
            // Create reviews for this location
            Review::factory()->create([
                'user_id' => $this->users[0]->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
                'rating' => 5,
                'would_visit_again' => true,
                'review_text' => 'Excellent!',
                'created_at' => now()->subDays(1),
            ]);

            Review::factory()->create([
                'user_id' => $this->users[1]->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
                'rating' => 3,
                'would_visit_again' => false,
                'review_text' => null,
                'created_at' => now()->subDays(2),
            ]);

            Review::factory()->create([
                'user_id' => $this->users[2]->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
                'rating' => 4,
                'would_visit_again' => true,
                'review_text' => 'Good experience',
                'created_at' => now()->subDays(3),
            ]);
        });

        it('can fetch reviews for a location', function () {
            $query = '
                query ReviewsByPlace($partner_location_id: ID!) {
                    reviewsByPlace(partner_location_id: $partner_location_id) {
                        data {
                            id
                            rating
                            would_visit_again
                            review_text
                            user {
                                id
                                name
                            }
                            deal {
                                id
                                title
                            }
                            partnerLocation {
                                id
                                name
                            }
                        }
                        paginatorInfo {
                            total
                            count
                            currentPage
                        }
                    }
                }
            ';

            $variables = [
                'partner_location_id' => (string) $this->partnerLocation->id,
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'reviewsByPlace' => [
                        'data' => [
                            [
                                'rating' => 5,
                                'would_visit_again' => true,
                                'review_text' => 'Excellent!',
                                'partnerLocation' => [
                                    'id' => (string) $this->partnerLocation->id,
                                    'name' => $this->partnerLocation->name,
                                ],
                            ],
                            [
                                'rating' => 3,
                                'would_visit_again' => false,
                                'review_text' => null,
                            ],
                            [
                                'rating' => 4,
                                'would_visit_again' => true,
                                'review_text' => 'Good experience',
                            ],
                        ],
                        'paginatorInfo' => [
                            'total' => 3,
                            'count' => 3,
                            'currentPage' => 1,
                        ],
                    ],
                ],
            ]);
        });

        it('can filter by rating', function () {
            $query = '
                query ReviewsByPlace($partner_location_id: ID!, $filters: ReviewFilters) {
                    reviewsByPlace(partner_location_id: $partner_location_id, filters: $filters) {
                        data {
                            rating
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            ';

            $variables = [
                'partner_location_id' => (string) $this->partnerLocation->id,
                'filters' => [
                    'rating' => 5,
                ],
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'reviewsByPlace' => [
                        'data' => [
                            ['rating' => 5],
                        ],
                        'paginatorInfo' => [
                            'total' => 1,
                        ],
                    ],
                ],
            ]);
        });

        it('can filter by minimum rating', function () {
            $query = '
                query ReviewsByPlace($partner_location_id: ID!, $filters: ReviewFilters) {
                    reviewsByPlace(partner_location_id: $partner_location_id, filters: $filters) {
                        data {
                            rating
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            ';

            $variables = [
                'partner_location_id' => (string) $this->partnerLocation->id,
                'filters' => [
                    'min_rating' => 4,
                ],
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'reviewsByPlace' => [
                        'data' => [
                            ['rating' => 5],
                            ['rating' => 4],
                        ],
                        'paginatorInfo' => [
                            'total' => 2,
                        ],
                    ],
                ],
            ]);
        });

        it('can filter by would_visit_again', function () {
            $query = '
                query ReviewsByPlace($partner_location_id: ID!, $filters: ReviewFilters) {
                    reviewsByPlace(partner_location_id: $partner_location_id, filters: $filters) {
                        data {
                            would_visit_again
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            ';

            $variables = [
                'partner_location_id' => (string) $this->partnerLocation->id,
                'filters' => [
                    'would_visit_again' => true,
                ],
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'reviewsByPlace' => [
                        'data' => [
                            ['would_visit_again' => true],
                            ['would_visit_again' => true],
                        ],
                        'paginatorInfo' => [
                            'total' => 2,
                        ],
                    ],
                ],
            ]);
        });

        it('can filter by has_review_text', function () {
            $query = '
                query ReviewsByPlace($partner_location_id: ID!, $filters: ReviewFilters) {
                    reviewsByPlace(partner_location_id: $partner_location_id, filters: $filters) {
                        data {
                            review_text
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            ';

            $variables = [
                'partner_location_id' => (string) $this->partnerLocation->id,
                'filters' => [
                    'has_review_text' => true,
                ],
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'reviewsByPlace' => [
                        'paginatorInfo' => [
                            'total' => 2,
                        ],
                    ],
                ],
            ]);

            $reviewTexts = collect($response->json('data.reviewsByPlace.data'))
                ->pluck('review_text')
                ->filter();
            
            expect($reviewTexts)->toHaveCount(2);
        });

        it('can sort by rating', function () {
            $query = '
                query ReviewsByPlace($partner_location_id: ID!, $sort_by: ReviewSortBy, $sort_order: SortOrder) {
                    reviewsByPlace(partner_location_id: $partner_location_id, sort_by: $sort_by, sort_order: $sort_order) {
                        data {
                            rating
                        }
                    }
                }
            ';

            $variables = [
                'partner_location_id' => (string) $this->partnerLocation->id,
                'sort_by' => 'RATING',
                'sort_order' => 'ASC',
            ];

            $response = $this->postGraphQL($query, $variables);

            $ratings = collect($response->json('data.reviewsByPlace.data'))->pluck('rating');
            expect($ratings->toArray())->toBe([3, 4, 5]);
        });

        it('supports pagination', function () {
            $query = '
                query ReviewsByPlace($partner_location_id: ID!, $first: Int, $page: Int) {
                    reviewsByPlace(partner_location_id: $partner_location_id, first: $first, page: $page) {
                        data {
                            rating
                        }
                        paginatorInfo {
                            total
                            count
                            currentPage
                            hasMorePages
                        }
                    }
                }
            ';

            $variables = [
                'partner_location_id' => (string) $this->partnerLocation->id,
                'first' => 2,
                'page' => 1,
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'reviewsByPlace' => [
                        'paginatorInfo' => [
                            'total' => 3,
                            'count' => 2,
                            'currentPage' => 1,
                            'hasMorePages' => true,
                        ],
                    ],
                ],
            ]);

            expect($response->json('data.reviewsByPlace.data'))->toHaveCount(2);
        });
    });

    describe('reviewsByUser query', function () {
        beforeEach(function () {
            $this->otherUser = User::factory()->create();
            
            // Create reviews for the authenticated user
            Review::factory()->count(2)->create([
                'user_id' => $this->user->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
            ]);

            // Create review for other user
            Review::factory()->create([
                'user_id' => $this->otherUser->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
            ]);
        });

        it('can fetch reviews for authenticated user', function () {
            $query = '
                query ReviewsByUser {
                    reviewsByUser {
                        data {
                            user {
                                id
                            }
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            ';

            $response = $this->postGraphQL($query);

            $response->assertJson([
                'data' => [
                    'reviewsByUser' => [
                        'data' => [
                            ['user' => ['id' => (string) $this->user->id]],
                            ['user' => ['id' => (string) $this->user->id]],
                        ],
                        'paginatorInfo' => [
                            'total' => 2,
                        ],
                    ],
                ],
            ]);
        });

        it('can fetch reviews for specific user', function () {
            $query = '
                query ReviewsByUser($user_id: ID) {
                    reviewsByUser(user_id: $user_id) {
                        data {
                            user {
                                id
                            }
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            ';

            $variables = [
                'user_id' => (string) $this->otherUser->id,
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'reviewsByUser' => [
                        'data' => [
                            ['user' => ['id' => (string) $this->otherUser->id]],
                        ],
                        'paginatorInfo' => [
                            'total' => 1,
                        ],
                    ],
                ],
            ]);
        });
    });

    describe('userReviewForDeal query', function () {
        it('returns user review for specific deal', function () {
            $review = Review::factory()->create([
                'user_id' => $this->user->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
                'rating' => 5,
                'review_text' => 'My review',
            ]);

            $query = '
                query UserReviewForDeal($deal_id: ID!) {
                    userReviewForDeal(deal_id: $deal_id) {
                        id
                        rating
                        review_text
                        user {
                            id
                        }
                        deal {
                            id
                        }
                    }
                }
            ';

            $variables = [
                'deal_id' => (string) $this->deal->id,
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'userReviewForDeal' => [
                        'id' => (string) $review->id,
                        'rating' => 5,
                        'review_text' => 'My review',
                        'user' => [
                            'id' => (string) $this->user->id,
                        ],
                        'deal' => [
                            'id' => (string) $this->deal->id,
                        ],
                    ],
                ],
            ]);
        });

        it('returns null when user has not reviewed the deal', function () {
            $query = '
                query UserReviewForDeal($deal_id: ID!) {
                    userReviewForDeal(deal_id: $deal_id) {
                        id
                    }
                }
            ';

            $variables = [
                'deal_id' => (string) $this->deal->id,
            ];

            $response = $this->postGraphQL($query, $variables);

            $response->assertJson([
                'data' => [
                    'userReviewForDeal' => null,
                ],
            ]);
        });
    });

    it('requires authentication', function () {
        $this->app['auth']->forgetGuards();

        $query = '
            query ReviewsByPlace($partner_location_id: ID!) {
                reviewsByPlace(partner_location_id: $partner_location_id) {
                    data {
                        id
                    }
                }
            }
        ';

        $variables = [
            'partner_location_id' => (string) $this->partnerLocation->id,
        ];

        $response = $this->postGraphQL($query, $variables);

        $response->assertGraphQLError('Unauthenticated.');
    });
});
