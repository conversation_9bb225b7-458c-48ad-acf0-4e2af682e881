<?php

use App\Enums\InvitationStatus;
use App\Enums\SocialLoginStatus;
use App\Models\Invitation;
use App\Models\User;
use Carbon\Carbon;

describe('SocialLoginMutation', function () {
    beforeEach(function () {
        config()->set('services.google.enabled', true);
        config()->set('services.apple.enabled', true);
        mockSocialiteDriver('google');
        mockSocialiteDriver('apple');
    });

    it('returns error for disabled provider', function () {
        config()->set('services.google.enabled', false);
        graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                status_code
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'invalid-token',
            ]
        )->assertGraphQLErrorMessage('google provider is not enabled');
    });

    it('returns error for invalid token', function () {
        graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                status_code
            }
        }',
            [
                'provider' => 'APPLE',
                'token' => 'invalid-token',
            ]
        )->assertGraphQLErrorMessage('Invalid social authentication.');
    });

    it('returns success for existing user', function () {
        $user = User::factory()->create(['email' => '<EMAIL>']);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => SocialLoginStatus::SUCCESS->name,
                ],
            ],
        ]);
    });

    it('creates user and pending invitation for new user', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
                message
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => SocialLoginStatus::PENDING_INVITATION->name,
                    'message' => 'Your invitation is pending approval. You will be notified when your account is approved.',
                ],
            ],
        ]);

        /** @var User $user */
        $user = User::where('email', '<EMAIL>')->first();
        expect($user)->not->toBeNull();

        /** @var Invitation $invitation */
        $invitation = Invitation::where('email', '<EMAIL>')->first();
        expect($invitation)
            ->not->toBeNull()
            ->and($invitation->status)->toBe(InvitationStatus::PENDING)
            ->and($invitation->expires_at)->toBeInstanceOf(Carbon::class);
    });

    it('renews expired invitation for new user', function () {
        /** @var Invitation */
        $invitation = Invitation::factory()
            ->expired()
            ->create(['email' => '<EMAIL>']);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
                message
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => SocialLoginStatus::PENDING_INVITATION->name,
                    'message' => 'Your invitation has been renewed and is pending approval.',
                ],
            ],
        ]);

        /** @var User $user */
        $user = User::where('email', '<EMAIL>')->first();
        expect($user)->not->toBeNull();

        /** @var Invitation $invitation */
        $invitation = $invitation->fresh();
        expect($invitation)
            ->not->toBeNull()
            ->and($invitation->status)->toBe(InvitationStatus::PENDING)
            ->and($invitation->expires_at)->toBeInstanceOf(Carbon::class)
            ->and($invitation->isExpired())->toBeFalse();
    });

    it('returns awaiting approval for pending invitation', function () {
        /** @var Invitation */
        $invitation = Invitation::factory()
            ->pending()
            ->create(['email' => '<EMAIL>']);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
                message
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => SocialLoginStatus::AWAITING_APPROVAL->name,
                    'message' => 'Your invitation is still pending approval.',
                ],
            ],
        ]);

        /** @var User $user */
        $user = User::where('email', '<EMAIL>')->first();
        expect($user)->not->toBeNull();
    });

    it('creates user and removes approved invitation', function () {
        /** @var Invitation */
        $invitation = Invitation::factory()
            ->approved()
            ->create(['email' => '<EMAIL>']);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => SocialLoginStatus::NEW_USER->name,
                ],
            ],
        ]);

        /** @var User $user */
        $user = User::where('email', '<EMAIL>')->first();
        expect($user)->not->toBeNull();

        expect(Invitation::find($invitation->id))
            ->not->toBeNull()
            ->and($invitation->fresh()->status)->toBe(InvitationStatus::APPROVED);
    });

    it('checks invitation for existing user', function () {
        $user = User::factory()->create(['email' => '<EMAIL>']);
        $invitation = Invitation::factory()
            ->pending()
            ->create(['email' => '<EMAIL>']);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
                message
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => 'AWAITING_APPROVAL',
                    'message' => 'Your invitation is still pending approval.',
                ],
            ],
        ]);
    });

    it('renews expired invitation for existing user', function () {
        $user = User::factory()->create(['email' => '<EMAIL>']);
        $invitation = Invitation::factory()
            ->expired()
            ->create(['email' => '<EMAIL>']);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
                message
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => 'PENDING_INVITATION',
                    'message' => 'Your invitation has been renewed and is pending approval.',
                ],
            ],
        ]);

        /** @var Invitation $invitation */
        $invitation = $invitation->fresh();
        expect($invitation)
            ->not->toBeNull()
            ->and($invitation->status)->toBe(InvitationStatus::PENDING)
            ->and($invitation->expires_at)->toBeInstanceOf(Carbon::class)
            ->and($invitation->isExpired())->toBeFalse();
    });

    it('removes approved invitation for existing user', function () {
        $user = User::factory()->create(['email' => '<EMAIL>']);
        $invitation = Invitation::factory()
            ->approved()
            ->create(['email' => '<EMAIL>']);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    email
                }
                status_code
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                    ],
                    'status_code' => SocialLoginStatus::SUCCESS->name,
                ],
            ],
        ]);

        expect(Invitation::find($invitation->id))
            ->not->toBeNull()
            ->and($invitation->fresh()->status)->toBe(InvitationStatus::APPROVED);
    });

    it('creates user with firstName and lastName', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!, $firstName: String, $lastName: String) {
            socialLogin(provider: $provider, token: $token, firstName: $firstName, lastName: $lastName) {
                token
                user {
                    email
                    name
                }
                status_code
                message
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
                'firstName' => 'John',
                'lastName' => 'Doe',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                        'name' => 'John Doe',
                    ],
                ],
            ],
        ]);
    });

    it('creates user with only firstName', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!, $firstName: String) {
            socialLogin(provider: $provider, token: $token, firstName: $firstName) {
                token
                user {
                    email
                    name
                }
                status_code
                message
            }
        }',
            [
                'provider' => 'GOOGLE',
                'token' => 'valid-token',
                'firstName' => 'Alice',
            ]
        );

        $response->assertJson([
            'data' => [
                'socialLogin' => [
                    'user' => [
                        'email' => '<EMAIL>',
                        'name' => 'Alice',
                    ],
                ],
            ],
        ]);
    });
});
