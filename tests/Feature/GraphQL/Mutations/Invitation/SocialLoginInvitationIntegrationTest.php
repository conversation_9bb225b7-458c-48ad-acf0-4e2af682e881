<?php

use App\Enums\InvitationStatus;
use App\Enums\SocialLoginStatus;
use App\Models\Invitation;
use App\Models\User;

it('handles social login with user invitation token', function () {
    config()->set('services.google.enabled', true);
    mockSocialiteDriver('google');

    // Create an inviter user
    /** @var User $inviter */
    $inviter = User::factory()->create();

    // Create invitation
    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
        'email' => '',
        'name' => '',
        'provider' => '',
        'provider_id' => '',
    ]);

    // First, user signs up via social login (creates user and pending admin invitation)
    $socialLoginResponse = graphQL(/** @lang GraphQL */ '
        mutation ($provider: SocialLoginProvider!, $token: String!) {
            socialLogin(provider: $provider, token: $token) {
                token
                user {
                    id
                    email
                }
                status_code
                message
            }
        }',
        [
            'provider' => 'GOOGLE',
            'token' => 'valid-token',
        ]
    );

    $socialLoginResponse->assertJson([
        'data' => [
            'socialLogin' => [
                'status_code' => SocialLoginStatus::PENDING_INVITATION->name,
                'message' => 'Your invitation is pending approval. You will be notified when your account is approved.',
            ],
        ],
    ]);

    // Get the created user
    /** @var User $user */
    $user = User::where('email', '<EMAIL>')->first();
    expect($user)->not->toBeNull();

    // Now user accepts the invitation token
    \Pest\Laravel\actingAs($user, 'sanctum');

    $acceptResponse = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                    email
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
    ]);

    $acceptResponse->assertJson([
        'data' => [
            'acceptInvitation' => [
                'user' => [
                    'id' => (string) $user->id,
                    'email' => $user->email,
                ],
                'statusCode' => 200,
            ],
        ],
    ]);

    // Verify the admin invitation was updated with referral and approved
    $userInvitation = Invitation::where('email', $user->email)
        ->first();

    expect($userInvitation)->not->toBeNull();
    $userInvitation->refresh();
    expect($userInvitation->inviter_user_id)->toBe($inviter->id)
        ->and($userInvitation->status)->toBe(InvitationStatus::APPROVED);

    // Verify the user invitation was marked as used
    $invitation->refresh();
    expect($invitation->is_used)->toBeTrue()
        ->and($invitation->invitee_user_id)->toBe($user->id);

    // Verify inviter's invitation count is updated
    $inviterInfo = $inviter->getInvitationInfo();
    expect($inviterInfo['remaining'])->toBe(4); // 5 - 1 = 4
});

it('prevents accepting invitation when user exceeds limit', function () {
    config()->set('services.google.enabled', true);
    mockSocialiteDriver('google');

    // Create inviter user with max invitations already used
    /** @var User $inviter */
    $inviter = User::factory()->create();
    $limit = config('invitation.user_invitation_limit', 5);

    // Create max invitations for inviter
    for ($i = 0; $i < $limit; $i++) {
        Invitation::factory()->create([
            'inviter_user_id' => $inviter->id,
            'invitee_user_id' => User::factory()->create()->id,
            'token' => Invitation::generateToken(),
            'token_expires_at' => now()->addDays(7),
            'is_used' => true,
        ]);
    }

    // Create one more invitation (this should fail when accepted)
    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    /** @var User $user */
    $user = User::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
    ]);

    $response->assertGraphQLErrorMessage('Inviter has exceeded their invitation limit');
});

it('allows user to check their invitation status and info', function () {
    config()->set('services.google.enabled', true);
    mockSocialiteDriver('google');

    /** @var User $user */
    $user = User::factory()->create();

    // Create some invitations for the user
    for ($i = 0; $i < 3; $i++) {
        Invitation::factory()->create([
            'inviter_user_id' => $user->id,
            'invitee_user_id' => User::factory()->create()->id,
            'token' => Invitation::generateToken(),
            'token_expires_at' => now()->addDays(7),
            'is_used' => true,
        ]);
    }

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query {
            me {
                invitations {
                    info {
                        total
                        remaining
                    }
                    data {
                        id
                        token
                        is_used
                        invitee {
                            id
                            email
                        }
                    }
                }
            }
        }
    ');

    $limit = config('invitation.user_invitation_limit', 5);
    $response->assertJson([
        'data' => [
            'me' => [
                'invitations' => [
                    'info' => [
                        'total' => $limit,
                        'remaining' => $limit - 3,
                    ],
                ],
            ],
        ],
    ]);

    $invitations = $response->json('data.me.invitations.data');
    expect($invitations)->toHaveCount(3);

    foreach ($invitations as $invitation) {
        expect($invitation['is_used'])->toBeTrue()
            ->and($invitation['invitee'])->not->toBeNull();
    }
});
