<?php

use App\Models\Invitation;
use App\Models\User;

it('allows user to create invitation', function () {
    /** @var User $user */
    $user = User::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation {
            inviteUser
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'inviteUser',
        ],
    ]);

    $inviteLink = $response->json('data.inviteUser');
    expect($inviteLink)->toHaveLength(config('invitation.token_length'));

    // Verify invitation was created in database
    $invitation = Invitation::where('inviter_user_id', $user->id)->first();
    expect($invitation)->not->toBeNull()
        ->and($invitation->token)->not->toBeNull()
        ->and($invitation->token_expires_at)->not->toBeNull()
        ->and($invitation->is_used)->toBeFalse();
});

it('prevents user from exceeding invitation limit', function () {
    /** @var User $user */
    $user = User::factory()->create();

    // Create max invitations for user
    $limit = config('invitation.user_invitation_limit', 5);

    for ($i = 0; $i < $limit; $i++) {
        Invitation::factory()->create([
            'inviter_user_id' => $user->id,
            'invitee_user_id' => User::factory()->create()->id,
            'token' => Invitation::generateToken(),
            'token_expires_at' => now()->addDays(7),
            'is_used' => true,
        ]);
    }

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation {
            inviteUser
        }
    ');

    $response->assertGraphQLErrorMessage('You have reached your invitation limit');
});

it('returns user invitation info correctly', function () {
    /** @var User $user */
    $user = User::factory()->create();

    // Create some used invitations
    for ($i = 0; $i < 2; $i++) {
        Invitation::factory()->create([
            'inviter_user_id' => $user->id,
            'invitee_user_id' => User::factory()->create()->id,
            'token' => Invitation::generateToken(),
            'token_expires_at' => now()->addDays(7),
            'is_used' => true,
        ]);
    }

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query {
            me {
                invitations {
                    info {
                        total
                        remaining
                    }
                    data {
                        id
                        token
                        is_used
                    }
                }
            }
        }
    ');

    $limit = config('invitation.user_invitation_limit', 5);
    $response->assertJson([
        'data' => [
            'me' => [
                'invitations' => [
                    'info' => [
                        'total' => $limit,
                        'remaining' => $limit - 2,
                    ],
                ],
            ],
        ],
    ]);

    // Check that we have exactly 2 invitations
    $invitations = $response->json('data.me.invitations.data');
    expect($invitations)->toHaveCount(2);
});
