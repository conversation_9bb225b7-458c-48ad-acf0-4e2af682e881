<?php

use App\Enums\InvitationStatus;
use App\Models\Invitation;
use App\Models\User;

it('allows user to accept valid invitation', function () {
    /** @var User $inviter */
    $inviter = User::factory()->create();
    /** @var User $invitee */
    $invitee = User::factory()->create();

    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    \Pest\Laravel\actingAs($invitee, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                    email
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
    ]);

    $response->assertJson([
        'data' => [
            'acceptInvitation' => [
                'user' => [
                    'id' => (string) $invitee->id,
                    'email' => $invitee->email,
                ],
                'statusCode' => 200,
            ],
        ],
    ]);

    // Verify invitation was updated
    $invitation->refresh();
    expect($invitation->is_used)->toBeTrue()
        ->and($invitation->invitee_user_id)->toBe($invitee->id)
        ->and($invitation->email)->toBe($invitee->email)
        ->and($invitation->status)->toBe(InvitationStatus::APPROVED);
});

it('rejects invalid invitation token', function () {
    /** @var User $inviter */
    $inviter = User::factory()->create();
    /** @var User $invitee */
    $invitee = User::factory()->create();

    \Pest\Laravel\actingAs($invitee, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => 'invalid-token',
    ]);

    $response->assertGraphQLErrorMessage('Invalid invitation token');
});

it('rejects expired invitation token', function () {
    /** @var User $inviter */
    $inviter = User::factory()->create();
    /** @var User $invitee */
    $invitee = User::factory()->create();

    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->subDays(1), // Expired
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    \Pest\Laravel\actingAs($invitee, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
    ]);

    $response->assertGraphQLErrorMessage('Invitation link has expired');
});

it('rejects already used invitation', function () {
    /** @var User $inviter */
    $inviter = User::factory()->create();
    /** @var User $invitee */
    $invitee = User::factory()->create();

    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => true, // Already used
        'status' => InvitationStatus::APPROVED,
    ]);

    \Pest\Laravel\actingAs($invitee, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
    ]);

    $response->assertGraphQLErrorMessage('Invitation has already been used');
});

it('handles user with existing pending admin invitation', function () {
    /** @var User $inviter */
    $inviter = User::factory()->create();
    /** @var User $invitee */
    $invitee = User::factory()->create();

    // Create existing admin invitation for user
    $adminInvitation = Invitation::factory()->create([
        'email' => $invitee->email,
        'status' => InvitationStatus::PENDING,
        'inviter_user_id' => null, // Admin invitation
        'token' => null,
    ]);

    // Create user invitation
    $userInvitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    \Pest\Laravel\actingAs($invitee, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                    email
                }
                statusCode
            }
        }
    ', [
        'token' => $userInvitation->token,
    ]);

    $response->assertJson([
        'data' => [
            'acceptInvitation' => [
                'user' => [
                    'id' => (string) $invitee->id,
                ],
                'statusCode' => 200,
            ],
        ],
    ]);

    // Verify admin invitation was deleted (current behavior in mutation)
    expect(Invitation::find($adminInvitation->id))->toBeNull();

    // Verify user invitation was marked as used
    $userInvitation->refresh();
    expect($userInvitation->is_used)->toBeTrue()
        ->and($userInvitation->invitee_user_id)->toBe($invitee->id);
});

it('rejects invitation when user is already approved', function () {
    /** @var User $inviter */
    $inviter = User::factory()->create();
    /** @var User $invitee */
    $invitee = User::factory()->create();

    // Create existing approved invitation for user
    Invitation::factory()->create([
        'email' => $invitee->email,
        'status' => InvitationStatus::APPROVED,
    ]);

    // Create user invitation
    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    \Pest\Laravel\actingAs($invitee, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
    ]);

    $response->assertGraphQLErrorMessage('User is already approved');
});

it('rejects invitation when inviter has exceeded their limit', function () {
    /** @var User $inviter */
    $inviter = User::factory()->create();
    /** @var User $invitee */
    $invitee = User::factory()->create();

    // Create max invitations for inviter
    $limit = config('invitation.user_invitation_limit', 5);
    for ($i = 0; $i < $limit; $i++) {
        Invitation::factory()->create([
            'inviter_user_id' => $inviter->id,
            'invitee_user_id' => User::factory()->create()->id,
            'token' => Invitation::generateToken(),
            'token_expires_at' => now()->addDays(7),
            'is_used' => true,
        ]);
    }

    // Create one more invitation (this should fail when accepted)
    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    \Pest\Laravel\actingAs($invitee, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($token: String!) {
            acceptInvitation(token: $token) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
    ]);

    $response->assertGraphQLErrorMessage('Inviter has exceeded their invitation limit');
});
