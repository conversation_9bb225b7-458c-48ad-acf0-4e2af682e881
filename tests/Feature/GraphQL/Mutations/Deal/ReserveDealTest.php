<?php

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\Tag;
use App\Models\User;
use App\Models\UserDeal;
use Carbon\Carbon;

use function Pest\Laravel\actingAs;

test('reserveDeal mutation saves deal snapshot', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    $reserveDate = now();
    $weekDay = now()->format('w');
    $fromTime = '14:00';
    $toTime = '18:00';

    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state([
            'day' => $weekDay,
            'from' => $fromTime,
            'to' => $toTime,
        ]))
        ->create([
            'title' => 'Test Deal',
            'description' => 'Test Description',
            'max_saving' => 50.00,
            'max_usage_per_day' => 10,
            'reuse_limit_days' => 7,
        ]);

    /** @var \Illuminate\Support\Collection<int, Tag> $tags */
    $tags = Tag::factory()->category()->count(2)->create();
    $deal->service_options()->sync($tags->pluck('id'));
    $deal = $deal->refresh();

    /** @var \App\Enums\DealType $dealType */
    $dealType = $deal->deal_type;

    $fromTimeParsed = Carbon::parse(now()->format('Y-m-d').' '.$fromTime);

    \Pest\Laravel\travelTo($fromTimeParsed->addMinutes(2));

    $response = graphQL(/** @lang GraphQL */ '
        mutation ReserveDeal($input: ReserveDealInput!) {
            reserveDeal(input: $input) {
                myDeal {
                    id
                    status
                    deal_snapshot {
                        title
                        description
                        deal_type
                        max_usage_per_day
                        reuse_limit_days
                        service_options {
                            id
                            title
                        }
                    }
                }
            }
        }
    ', [
        'input' => [
            'id' => $deal->id,
            'reserve_slot' => [
                'date' => $reserveDate->format('Y-m-d'),
                'slot' => [
                    'from' => $fromTime,
                    'to' => $toTime,
                ],
            ],
        ],
    ]);

    $response->assertJson([
        'data' => [
            'reserveDeal' => [
                'myDeal' => [
                    'status' => 'REDEEMABLE',
                    'deal_snapshot' => [
                        'title' => 'Test Deal',
                        'description' => 'Test Description',
                        'deal_type' => $dealType->name,
                        'max_usage_per_day' => 10,
                        'reuse_limit_days' => 7,
                        'service_options' => [
                            [
                                'id' => (string) $tags[0]->id,
                                'title' => $tags[0]->title,
                            ],
                            [
                                'id' => (string) $tags[1]->id,
                                'title' => $tags[1]->title,
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ]);

    // Verify the snapshot is stored in the database
    $userDeal = UserDeal::first();
    expect($userDeal->deal_snapshot)->toBeArray()
        ->and($userDeal->deal_snapshot['title'])->toBe('Test Deal')
        ->and($userDeal->deal_snapshot['description'])->toBe('Test Description')
        ->and($userDeal->deal_snapshot['max_saving'])->toEqual(50.00)
        ->and($userDeal->deal_snapshot['max_usage_per_day'])->toBe(10)
        ->and($userDeal->deal_snapshot['reuse_limit_days'])->toBe(7)
        ->and($userDeal->deal_snapshot['service_options'])->toBeArray()
        ->and(count($userDeal->deal_snapshot['service_options']))->toBe(2);
});
