<?php

declare(strict_types=1);

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\User;
use App\Models\UserDeal;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\Sanctum;

// TODO: Fail to reserve with no available seats
// TODO: Success to renew a deal

it('fails for unauthenticated user', function () {

    /** @var Deal */
    $deal = Deal::factory()->create();

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                }
            }
        }
    ', [
        'id' => $deal->id,
        'date' => now()->addDay()->format('Y-m-d'),
        'from' => '00:00',
        'to' => '02:00',
    ]);

    $response->assertGraphQLErrorMessage('Unauthenticated.');
});

describe('ReserveDealMutation', function () {

    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        Sanctum::actingAs($this->user);
    });

    test('valid Deal ID with authenticated user', function () {

        graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal(
                    input: {id: $id, reserve_slot: {date: $date, slot: {from: $from, to: $to}}, myDealIdToRenew: null}
                ) {
                    myDeal {
                        id
                        status
                    }
                }
            }',
            [
                'id' => $this->deal->id,
                'date' => now()->addDay()->format('Y-m-d'),
                'from' => now()->addDay()->format('Y-m-d 00:00'),
                'to' => now()->addDay()->format('Y-m-d 02:00'),
            ])->assertJson([
                'data' => [
                    'reserveDeal' => [
                        'myDeal' => [
                            'id' => UserDeal::latest('id')->value('id'),
                            'status' => 'UPCOMING',
                        ],
                    ],
                ],
            ]);
    });

    it('fails with invalid deal ID', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation {
            reserveDeal (input: {
                id: "invalid_id",
                reserve_slot: {
                    date: "2025-04-05",
                    slot: {
                        from: "00:00",
                        to: "02:00"
                    }
                }
            }) {
                myDeal {
                    id
                }
            }
        }');

        $response->assertGraphQLErrorMessage('Validation failed for the field [reserveDeal].');
    });

    it('reserves a deal successfully', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    reserved_at
                    reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
            }
        }', [
            'id' => $this->deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id',
                        'status',
                        'deal' => [
                            'id',
                        ],
                        'reserved_at',
                        'reserve_slot' => [
                            'date',
                            'slot' => [
                                'from',
                                'to',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $reservedAt = $response->json('data.reserveDeal.myDeal.reserved_at');
        $this->assertNotNull($reservedAt);

        expect($response->json('data.reserveDeal.myDeal.status'))->toBe('UPCOMING');
    });

    it('reserves a deal in same slots day successfully before slot time comes', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '18:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        \Carbon\Carbon::setTestNow(now()->toDateString().' 13:00');

        $response = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                        reserved_at
                        reserve_slot {
                            date
                            slot {
                                from
                                to
                            }
                        }
                    }
                }
            }', [
            'id' => $deal->id,
            'date' => now()->format('Y-m-d'),
            'from' => now()->format('Y-m-d 14:00'),
            'to' => now()->format('Y-m-d 18:00'),
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id',
                        'status',
                        'deal' => [
                            'id',
                        ],
                        'reserved_at',
                        'reserve_slot' => [
                            'date',
                            'slot' => [
                                'from',
                                'to',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $reservedAt = $response->json('data.reserveDeal.myDeal.reserved_at');
        $this->assertNotNull($reservedAt);

        expect($response->json('data.reserveDeal.myDeal.status'))->toBe('UPCOMING');
    });

    it('reserves a deal in same slots day successfully after slot time comes', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '18:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        \Carbon\Carbon::setTestNow(now()->toDateString().' 19:00');

        $response = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                        reserved_at
                        reserve_slot {
                            date
                            slot {
                                from
                                to
                            }
                        }
                    }
                }
            }', [
            'id' => $deal->id,
            'date' => now()->format('Y-m-d'),
            'from' => now()->format('Y-m-d 14:00'),
            'to' => now()->format('Y-m-d 18:00'),
        ])->assertGraphQLErrorMessage('Invalid reserve slot. The date must be in the future.');
    });

    it('reserves a deal in same slots day successfully between slot time comes', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '18:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        \Carbon\Carbon::setTestNow(now()->toDateString().' 16:00');

        $response = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                        reserved_at
                        reserve_slot {
                            date
                            slot {
                                from
                                to
                            }
                        }
                    }
                }
            }', [
            'id' => $deal->id,
            'date' => now()->format('Y-m-d'),
            'from' => now()->format('Y-m-d 14:00'),
            'to' => now()->format('Y-m-d 18:00'),
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id',
                        'status',
                        'deal' => [
                            'id',
                        ],
                        'reserved_at',
                        'reserve_slot' => [
                            'date',
                            'slot' => [
                                'from',
                                'to',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $reservedAt = $response->json('data.reserveDeal.myDeal.reserved_at');
        $this->assertNotNull($reservedAt);

        expect($response->json('data.reserveDeal.myDeal.status'))->toBe('REDEEMABLE');
    });

    it('Fail to reserve with no available seats', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 1,
            ]);

        UserDeal::factory()->create([
            'user_id' => User::factory()->create()->id,
            'deal_id' => $deal->id,
            'reserve_slot' => [
                'date' => now()->addDay()->format('Y-m-d'),
                'slot' => [
                    'from' => '00:00',
                    'to' => '02:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    reserved_at
                    reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
            }
        }', [
            'id' => $deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ])->assertGraphQLErrorMessage('There is no available seats left for this deal');
    });

    it('fails with invalid time slot', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                }
            }
        }', [
            'id' => $this->deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 23:00'),
            'to' => now()->addDay()->format('Y-m-d 01:00'),
        ]);

        $response->assertGraphQLErrorMessage('Invalid reserve slot. The slot is not available for this deal');
    });

    it('fails with past date reservation', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal(
                input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }
            ) {
                myDeal {
                    id
                }
            }
        }', [
            'id' => $this->deal->id,
            'date' => now()->subDay()->format('Y-m-d'),
            'from' => now()->subDay()->format('Y-m-d 00:00'),
            'to' => now()->subDay()->format('Y-m-d 02:00'),
        ]);

        $response->assertGraphQLErrorMessage('Invalid reserve slot. The date must be in the future.');
    });

    it('fails when trying to renew a reserved deal', function () {
        $myDeal = UserDeal::create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'reserved_at' => now(),
            'reserve_slot' => [
                'date' => now()->addDay()->format('Y-m-d'),
                'slot' => [
                    'from' => '00:00',
                    'to' => '02:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!, $myDealIdToRenew: ID!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                },
                myDealIdToRenew: $myDealIdToRenew
            }) {
                myDeal {
                    id
                }
            }
        }
    ', [
            'id' => $this->deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
            'myDealIdToRenew' => $myDeal->id,
        ]);

        $response->assertGraphQLErrorMessage('Can\'t renew this deal before x days.');
    })->todo('With @Hazem');

    it('it cache my deal query and flush after mutation', function () {
        \Carbon\Carbon::setTestNow(now()->toDateString().' 01:00:00');
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    reserved_at
                    reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
            }
        }', [
            'id' => $deal->id,
            'date' => now()->toDateString(),
            'from' => now()->format('Y-m-d 00:00'),
            'to' => now()->format('Y-m-d 02:00'),
        ]);

        expect($response)->toBeObject()
            ->and($response->json('data.reserveDeal.myDeal.status'))->toBe('REDEEMABLE');

        $myDealId = $response->json('data.reserveDeal.myDeal.id');

        // query myDeal by id
        graphQL(/** @lang GraphQL */ '
                query ($id: ID!) {
                    myDeal(id: $id) {
                        id
                        status
                    }
                }
            ', ['id' => (int) $myDealId])
            ->assertJson([
                'data' => [
                    'myDeal' => [
                        'id' => $myDealId,
                        'status' => 'REDEEMABLE',
                    ],
                ],
            ]);

        // lighthouse:auth:28:Query::myDeal:id:1
        expect(Cache::get("lighthouse:auth:{$this->user->id}:Query::myDeal:id:$myDealId"))->not->toBeNull();

        /** @var UserDeal $userDeal */
        $userDeal = UserDeal::query()->latest('id')->first();

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($myDealId: ID!) {
            redeemDeal (input: {
                myDealId: $myDealId
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    redeemed_at
                }
            }
        }
    ', [
            'myDealId' => $userDeal->id,
        ]);

        // dd($response->json());
        expect($response->json('data.redeemDeal.myDeal.status'))
            ->toBe('REDEEMED');

        expect(Cache::get("lighthouse:auth:{$this->user->id}:Query::myDeal:id:$myDealId"))->toBeNull();
    })->group('cache')->skip('Disable cache test for now');

    it('Re-reserves a deal deleted deal', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        UserDeal::factory()->redeemed()->create([
            'reuse_after' => now()->subDays(3)->format('Y-m-d H:i'),
            'reserve_slot' => [
                'date' => now()->addDay()->format('Y-m-d'),
                'slot' => [
                    'from' => '00:00',
                    'to' => '02:00',
                ],
            ],
        ]);

        $deal->delete();

        $deal->refresh();
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    reserved_at
                    reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
            }
        }', [
            'id' => $deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]);

        $response->assertGraphQLErrorMessage('Sorry the original deal has been deleted');
    });

    it('fails when user tries to book another deal at the same restaurant while having an active deal', function () {
        // Create a restaurant (PartnerLocation)
        /** @var \App\Models\PartnerLocation $restaurant */
        $restaurant = \App\Models\PartnerLocation::factory()->create(['name' => 'Test Restaurant']);

        // Create two different deals at the same restaurant
        /** @var Deal $firstDeal */
        $firstDeal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'partner_location_id' => $restaurant->id,
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        /** @var Deal $secondDeal */
        $secondDeal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '16:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'partner_location_id' => $restaurant->id,
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        // Book the first deal
        $firstResponse = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                    }
                }
            }', [
            'id' => $firstDeal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]);

        $firstResponse->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'status' => 'UPCOMING',
                    ],
                ],
            ],
        ]);

        // Try to book the second deal at the same restaurant
        $secondResponse = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                    }
                }
            }', [
            'id' => $secondDeal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 14:00'),
            'to' => now()->addDay()->format('Y-m-d 16:00'),
        ]);

        $secondResponse->assertGraphQLErrorMessage('You can only book one deal per restaurant at a time. Redeem or cancel the other deal to book this deal');
    });

    it('allows user to book another deal at the same restaurant after previous deal is no-show', function () {
        /** @var \App\Models\PartnerLocation $restaurant */
        $restaurant = \App\Models\PartnerLocation::factory()->create(['name' => 'Test Restaurant']);

        /** @var Deal $firstDeal */
        $firstDeal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'partner_location_id' => $restaurant->id,
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        /** @var Deal $secondDeal */
        $secondDeal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '16:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'partner_location_id' => $restaurant->id,
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        // Book the first deal
        $firstResponse = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                    }
                }
            }', [
            'id' => $firstDeal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]);

        $firstDealId = $firstResponse->json('data.reserveDeal.myDeal.id');
        $userDeal = \App\Models\UserDeal::find($firstDealId);
        $userDeal->status = 'no-show';
        $userDeal->save();

        // Try to book the second deal at the same restaurant (should succeed)
        $secondResponse = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                    }
                }
            }', [
            'id' => $secondDeal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 14:00'),
            'to' => now()->addDay()->format('Y-m-d 16:00'),
        ]);

        $secondResponse->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'status' => 'UPCOMING',
                    ],
                ],
            ],
        ]);
    });

    it('allows user to book deals at different restaurants simultaneously', function () {
        // Create two different restaurants
        /** @var \App\Models\PartnerLocation $restaurant1 */
        $restaurant1 = \App\Models\PartnerLocation::factory()->create(['name' => 'Restaurant 1']);
        /** @var \App\Models\PartnerLocation $restaurant2 */
        $restaurant2 = \App\Models\PartnerLocation::factory()->create(['name' => 'Restaurant 2']);

        // Create deals at different restaurants
        /** @var Deal $deal1 */
        $deal1 = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'partner_location_id' => $restaurant1->id,
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        /** @var Deal $deal2 */
        $deal2 = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '16:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'partner_location_id' => $restaurant2->id,
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        // Book the first deal
        $firstResponse = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                    }
                }
            }', [
            'id' => $deal1->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]);

        $firstResponse->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'status' => 'UPCOMING',
                    ],
                ],
            ],
        ]);

        // Book the second deal at a different restaurant (should succeed)
        $secondResponse = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                    }
                }
            }', [
            'id' => $deal2->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 14:00'),
            'to' => now()->addDay()->format('Y-m-d 16:00'),
        ]);

        $secondResponse->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'status' => 'UPCOMING',
                    ],
                ],
            ],
        ]);
    });
});
