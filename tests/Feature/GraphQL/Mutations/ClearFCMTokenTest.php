<?php

use App\Models\User;
use Illuminate\Support\Facades\Hash;

$mutation = <<<'GRAPHQL'
mutation clearFCMToken {
  clearFCMToken {
    id
    fcm_token
  }
}
GRAPHQL;

test('unauthenticated user cannot clear fcm token', function () use ($mutation) {
    $response = graphQl($mutation);
    expect($response->json('errors.0.message'))->toBe('Unauthenticated.');
});

test('authenticated user can clear their fcm token', function () use ($mutation) {
    $user = User::factory()->create([
        'password' => Hash::make('password'),
        'fcm_token' => 'existing-fcm-token',
    ]);
    $token = $user->createToken('test-token')->plainTextToken;
    $response = graphQl(
        $mutation,
        [],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($response->json('data.clearFCMToken.fcm_token'))->toBeNull();
});

test('user can clear their fcm token even if it is already null', function () use ($mutation) {
    $user = User::factory()->create([
        'password' => Hash::make('password'),
        'fcm_token' => null,
    ]);
    $token = $user->createToken('test-token')->plainTextToken;
    $response = graphQl(
        $mutation,
        [],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($response->json('data.clearFCMToken.fcm_token'))->toBeNull();
});

test('user can set and then clear their fcm token', function () use ($mutation) {
    $user = User::factory()->create([
        'password' => Hash::make('password'),
    ]);
    $token = $user->createToken('test-token')->plainTextToken;

    // First set the FCM token
    $setMutation = <<<'GRAPHQL'
    mutation addFCMToken($token: String!) {
      addFCMToken(token: $token) {
        id
        fcm_token
      }
    }
    GRAPHQL;

    $setResponse = graphQl(
        $setMutation,
        ['token' => 'test-fcm-token'],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($setResponse->json('data.addFCMToken.fcm_token'))->toBe('test-fcm-token');

    // Then clear the FCM token
    $clearResponse = graphQl(
        $mutation,
        [],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($clearResponse->json('data.clearFCMToken.fcm_token'))->toBeNull();
});
