<?php

use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use App\Models\UserDeal;
use Laravel\Sanctum\Sanctum;


describe('Review GraphQL Mutations', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->partnerLocation = PartnerLocation::factory()->create();
        $this->deal = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);

        // Create a redeemed user deal
        UserDeal::factory()->create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'redeemed_at' => now(),
        ]);

        Sanctum::actingAs($this->user);
    });

    describe('submitReview mutation', function () {
        it('can submit a complete review', function () {
            $mutation = '
                mutation SubmitReview($input: SubmitReviewInput!) {
                    submitReview(input: $input) {
                        review {
                            id
                            rating
                            savings_amount
                            would_visit_again
                            review_text
                            user {
                                id
                                name
                            }
                            deal {
                                id
                                title
                            }
                            partnerLocation {
                                id
                                name
                            }
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'deal_id' => (string) $this->deal->id,
                    'rating' => 5,
                    'savings_amount' => 25.50,
                    'would_visit_again' => true,
                    'review_text' => 'Excellent experience!',
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertJson([
                'data' => [
                    'submitReview' => [
                        'review' => [
                            'rating' => 5,
                            'savings_amount' => 25.50,
                            'would_visit_again' => true,
                            'review_text' => 'Excellent experience!',
                            'user' => [
                                'id' => (string) $this->user->id,
                                'name' => $this->user->name,
                            ],
                            'deal' => [
                                'id' => (string) $this->deal->id,
                                'title' => $this->deal->title,
                            ],
                            'partnerLocation' => [
                                'id' => (string) $this->partnerLocation->id,
                                'name' => $this->partnerLocation->name,
                            ],
                        ],
                        'message' => 'Review submitted successfully!',
                    ],
                ],
            ]);

            expect(Review::count())->toBe(1);
        });

        it('can submit a minimal review', function () {
            $mutation = '
                mutation SubmitReview($input: SubmitReviewInput!) {
                    submitReview(input: $input) {
                        review {
                            rating
                            savings_amount
                            would_visit_again
                            review_text
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'deal_id' => (string) $this->deal->id,
                    'rating' => 3,
                    'would_visit_again' => false,
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertJson([
                'data' => [
                    'submitReview' => [
                        'review' => [
                            'rating' => 3,
                            'savings_amount' => null,
                            'would_visit_again' => false,
                            'review_text' => null,
                        ],
                        'message' => 'Review submitted successfully!',
                    ],
                ],
            ]);
        });

        it('fails when user has not redeemed the deal', function () {
            $unredeemed = Deal::factory()->create(['partner_location_id' => $this->partnerLocation->id]);

            $mutation = '
                mutation SubmitReview($input: SubmitReviewInput!) {
                    submitReview(input: $input) {
                        review {
                            id
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'deal_id' => (string) $unredeemed->id,
                    'rating' => 5,
                    'would_visit_again' => true,
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertGraphQLErrorMessage('You can only review deals that you have redeemed.');
        });

        it('fails when user has already reviewed the deal', function () {
            Review::factory()->create([
                'user_id' => $this->user->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
            ]);

            $mutation = '
                mutation SubmitReview($input: SubmitReviewInput!) {
                    submitReview(input: $input) {
                        review {
                            id
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'deal_id' => (string) $this->deal->id,
                    'rating' => 5,
                    'would_visit_again' => true,
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertGraphQLErrorMessage('You have already reviewed this deal.');
        });

        it('validates rating is between 1 and 5', function () {
            $mutation = '
                mutation SubmitReview($input: SubmitReviewInput!) {
                    submitReview(input: $input) {
                        review {
                            id
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'deal_id' => (string) $this->deal->id,
                    'rating' => 6,
                    'would_visit_again' => true,
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertGraphQLErrorMessage('Validation failed for the field [submitReview].');
        });

        it('validates savings_amount is positive', function () {
            $mutation = '
                mutation SubmitReview($input: SubmitReviewInput!) {
                    submitReview(input: $input) {
                        review {
                            id
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'deal_id' => (string) $this->deal->id,
                    'rating' => 5,
                    'savings_amount' => -10.00,
                    'would_visit_again' => true,
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertGraphQLErrorMessage('Validation failed for the field [submitReview].');
        });

        it('validates review_text length', function () {
            $mutation = '
                mutation SubmitReview($input: SubmitReviewInput!) {
                    submitReview(input: $input) {
                        review {
                            id
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'deal_id' => (string) $this->deal->id,
                    'rating' => 5,
                    'would_visit_again' => true,
                    'review_text' => str_repeat('a', 1001),
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertGraphQLErrorMessage('Validation failed for the field [submitReview].');
        });
    });

    describe('updateReview mutation', function () {
        beforeEach(function () {
            $this->review = Review::factory()->create([
                'user_id' => $this->user->id,
                'deal_id' => $this->deal->id,
                'partner_location_id' => $this->partnerLocation->id,
                'rating' => 3,
                'savings_amount' => 15.00,
                'would_visit_again' => false,
                'review_text' => 'Original review',
            ]);
        });

        it('can update all review fields', function () {
            $mutation = '
                mutation UpdateReview($input: UpdateReviewInput!) {
                    updateReview(input: $input) {
                        review {
                            id
                            rating
                            savings_amount
                            would_visit_again
                            review_text
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'id' => (string) $this->review->id,
                    'rating' => 5,
                    'savings_amount' => 30.00,
                    'would_visit_again' => true,
                    'review_text' => 'Updated review text',
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertJson([
                'data' => [
                    'updateReview' => [
                        'review' => [
                            'id' => (string) $this->review->id,
                            'rating' => 5,
                            'savings_amount' => 30.00,
                            'would_visit_again' => true,
                            'review_text' => 'Updated review text',
                        ],
                        'message' => 'Review updated successfully!',
                    ],
                ],
            ]);
        });

        it('can update only specific fields', function () {
            $mutation = '
                mutation UpdateReview($input: UpdateReviewInput!) {
                    updateReview(input: $input) {
                        review {
                            rating
                            savings_amount
                            would_visit_again
                            review_text
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'id' => (string) $this->review->id,
                    'rating' => 4,
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertJson([
                'data' => [
                    'updateReview' => [
                        'review' => [
                            'rating' => 4,
                            'savings_amount' => 15.00, // unchanged
                            'would_visit_again' => false, // unchanged
                            'review_text' => 'Original review', // unchanged
                        ],
                        'message' => 'Review updated successfully!',
                    ],
                ],
            ]);
        });

        it('fails when user does not own the review', function () {
            $otherUser = User::factory()->create();
            Sanctum::actingAs($otherUser);

            $mutation = '
                mutation UpdateReview($input: UpdateReviewInput!) {
                    updateReview(input: $input) {
                        review {
                            id
                        }
                        message
                    }
                }
            ';

            $variables = [
                'input' => [
                    'id' => (string) $this->review->id,
                    'rating' => 5,
                ],
            ];

            $response = graphQL($mutation, $variables);

            $response->assertGraphQLErrorMessage('You can only update your own reviews.');
        });
    });

    it('requires authentication', function () {
        $this->app['auth']->forgetGuards();

        $mutation = '
            mutation SubmitReview($input: SubmitReviewInput!) {
                submitReview(input: $input) {
                    review {
                        id
                    }
                    message
                }
            }
        ';

        $variables = [
            'input' => [
                'deal_id' => (string) $this->deal->id,
                'rating' => 5,
                'would_visit_again' => true,
            ],
        ];

        $response = graphQL($mutation, $variables);

        $response->assertGraphQLErrorMessage('Unauthenticated.');
    });
});
