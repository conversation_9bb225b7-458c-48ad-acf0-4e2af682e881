<?php

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\UserDeal;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;

beforeEach(function () {
    Notification::fake();
});

test('user can reserve deal again after redeeming and waiting for reuse time', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    // Create reservation date and calculate the day of week
    $reserveDate = now()->addDays(2);
    $weekDay = $reserveDate->format('w');
    $fromTime = '14:00';
    $toTime = '18:00';

    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state([
            'day' => $weekDay,
            'from' => $fromTime,
            'to' => $toTime,
        ]))
        ->create([
            'title' => 'Test Deal',
            'description' => 'Test Description',
            'max_saving' => 50.00,
            'max_usage_per_day' => 10,
            'reuse_limit_days' => 7,
        ]);

    $reserveFrom = $reserveDate->format('Y-m-d').' '.$fromTime;
    $reserveTo = $reserveDate->format('Y-m-d').' '.$toTime;

    \Pest\Laravel\travelTo(Carbon::parse($reserveFrom)->addMinutes(10));

    // First reservation
    $firstReservationResponse = graphQL(/** @lang GraphQL */ '
        mutation ReserveDeal($input: ReserveDealInput!) {
            reserveDeal(input: $input) {
                myDeal {
                    id
                    status
                    reuse_after
                }
            }
        }
    ', [
        'input' => [
            'id' => $deal->id,
            'reserve_slot' => [
                'date' => $reserveDate->format('Y-m-d'),
                'slot' => [
                    'from' => $reserveFrom,
                    'to' => $reserveTo,
                ],
            ],
        ],
    ]);

    $firstReservationResponse->assertJson([
        'data' => [
            'reserveDeal' => [
                'myDeal' => [
                    'status' => 'REDEEMABLE',
                ],
            ],
        ],
    ]);

    $firstMyDealId = $firstReservationResponse->json('data.reserveDeal.myDeal.id');

    /** @var UserDeal $firstUserDeal */
    $firstUserDeal = UserDeal::find($firstMyDealId);

    expect($firstUserDeal->status)
        ->toBe('redeemable');

    // Redeem the first deal
    $redeemResponse = graphQL(/** @lang GraphQL */ '
        mutation ($myDealId: ID!) {
            redeemDeal (input: {
                myDealId: $myDealId
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    redeemed_at
                }
            }
        }
    ', [
        'myDealId' => $firstMyDealId,
    ]);

    expect($redeemResponse->json('data.redeemDeal.myDeal.status'))
        ->toBe('REDEEMED')
        ->and($firstUserDeal->refresh()->status)
        ->toBe('redeemed');

    // Check myDeals query - should show only the redeemed deal
    $myDealsBeforeReuse = graphQL(/** @lang GraphQL */ '
        query {
            myDeals(first: 10) {
                data {
                    id
                    status
                    deal {
                        id
                    }
                    redeemed_at
                }
            }
        }
    ');

    $myDealsBeforeReuse->assertJson([
        'data' => [
            'myDeals' => [
                'data' => [
                    [
                        'id' => $firstMyDealId,
                        'status' => 'REDEEMED',
                        'deal' => [
                            'id' => $deal->id,
                        ],
                    ],
                ],
            ],
        ],
    ]);

    // Travel past the reuse_after time to allow re-reservation
    \Pest\Laravel\travelTo(\Carbon\Carbon::parse($firstUserDeal->reuse_after)->format('Y-m-d').' '.$fromTime);

    // Create a new reservation date for the second reservation
    $secondReserveDate = data_get($deal->available_slots, '*.date')[0];

    // Try to reserve the same deal again - use the next valid date
    $secondReservationResponse = graphQL(/** @lang GraphQL */ '
        mutation ReserveDeal($input: ReserveDealInput!) {
            reserveDeal(input: $input) {
                myDeal {
                    id
                    status
                    reuse_after
                }
            }
        }
    ', [
        'input' => [
            'id' => $deal->id,
            'reserve_slot' => [
                'date' => $secondReserveDate,
                'slot' => [
                    'from' => $secondReserveDate.' '.$fromTime,
                    'to' => $secondReserveDate.' '.$toTime,
                ],
            ],
        ],
    ]);

    $secondReservationResponse->assertJson([
        'data' => [
            'reserveDeal' => [
                'myDeal' => [
                    'status' => 'REDEEMABLE',
                ],
            ],
        ],
    ]);

    $secondMyDealId = $secondReservationResponse->json('data.reserveDeal.myDeal.id');

    // Verify we have two different MyDeal records
    expect($secondMyDealId)->not->toBe($firstMyDealId);

    // Check myDeals query - should now show both the redeemed deal and the new reserved deal
    $myDealsAfterReuse = graphQL(/** @lang GraphQL */ '
        query {
            myDeals(first: 10) {
                data {
                    id
                    status
                    deal {
                        id
                    }
                    redeemed_at
                }
                paginatorInfo {
                    total
                }
            }
        }
    ');

    $myDealsAfterReuse->assertJson([
        'data' => [
            'myDeals' => [
                'data' => [
                    [
                        'id' => $secondMyDealId,
                        'status' => 'REDEEMABLE',
                        'deal' => [
                            'id' => $deal->id,
                        ],
                    ],
                ],
                'paginatorInfo' => [
                    'total' => 1, // Make Sure that only one user_deal will be returned from the same deal
                ],
            ],
        ],
    ]);

    // Verify both UserDeal records exist in the database
    $firstUserDealRecord = UserDeal::find($firstMyDealId);
    $secondUserDealRecord = UserDeal::find($secondMyDealId);

    expect($firstUserDealRecord)->not->toBeNull()
        ->and($firstUserDealRecord->status)->toBe('redeemed')
        ->and($firstUserDealRecord->redeemed_at)->not->toBeNull()
        ->and($secondUserDealRecord)->not->toBeNull()
        ->and($secondUserDealRecord->status)->toBe('redeemable')
        ->and($secondUserDealRecord->redeemed_at)->toBeNull();
});
