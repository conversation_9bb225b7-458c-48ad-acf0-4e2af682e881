<?php

use App\Models\Deal;
use App\Models\DealSlot;
use Carbon\Carbon;

test('availableSlots only shows slots where slot_to is more than 1 hour in the future', function () {
    // Set a fixed time for testing
    Carbon::setTestNow(Carbon::parse('2025-01-15 14:00:00'));

    /** @var Deal $deal */
    $deal = Deal::factory()->create([
        'max_usage_per_day' => 10,
    ]);

    // Create slots for today (Wednesday, day 3)
    $today = Carbon::now()->format('w'); // 3 for Wednesday

    // Slot that ended 2 hours ago (should be filtered out)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '10:00',
        'to' => '12:00', // 2 hours ago
    ]);

    // Slot that ended 30 minutes ago (should be filtered out)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '12:30',
        'to' => '13:30', // 30 minutes ago
    ]);

    // Slot that ends in 30 minutes (should be filtered out)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '13:45',
        'to' => '14:30', // ends in 30 minutes
    ]);

    // Slot that ends in 1 hour (should be filtered out)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '13:50',
        'to' => '15:00', // ends in 1 hour
    ]);

    // Slot that ends in 2 hours (should be included)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '14:30',
        'to' => '16:00', // ends in 2 hours
    ]);

    $availableSlots = $deal->available_slots;

    // Should only have slots for today
    expect($availableSlots)->toHaveCount(1);

    $todaySlots = $availableSlots[$today];
    expect($todaySlots['slots'])->toHaveCount(1); // Only 1 slot should be included (future by more than 1 hour)

    // Verify the filtered out slots are not present
    $slotTimes = collect($todaySlots['slots'])->map(function ($slot) {
        return [
            'from' => Carbon::parse($slot['from'])->format('H:i'),
            'to' => Carbon::parse($slot['to'])->format('H:i'),
        ];
    })->toArray();

    expect($slotTimes)->not->toContain([
        'from' => '10:00',
        'to' => '12:00',
    ]);

    expect($slotTimes)->not->toContain([
        'from' => '12:30',
        'to' => '13:30',
    ]);

    expect($slotTimes)->not->toContain([
        'from' => '13:45',
        'to' => '14:30',
    ]);

    expect($slotTimes)->not->toContain([
        'from' => '13:50',
        'to' => '15:00',
    ]);

    // Verify the included slot is present
    expect($slotTimes)->toContain([
        'from' => '14:30',
        'to' => '16:00',
    ]);
});

test('availableSlots handles overnight slots correctly', function () {
    // Set a fixed time for testing - 03:00 AM
    Carbon::setTestNow(Carbon::parse('2025-01-15 03:00:00'));

    /** @var Deal $deal */
    $deal = Deal::factory()->create([
        'max_usage_per_day' => 10,
    ]);

    $today = Carbon::now()->format('w'); // 3 for Wednesday

    // Overnight slot that ends in 30 minutes (should be filtered out)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '02:00',
        'to' => '03:30', // ends in 30 minutes
    ]);

    // Overnight slot that ends in 2 hours (should be included)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '03:00',
        'to' => '05:00', // ends in 2 hours
    ]);

    $availableSlots = $deal->available_slots;
    $todaySlots = $availableSlots[$today];

    // Should only have 1 slot (the one that ends in the future by more than 1 hour)
    expect($todaySlots['slots'])->toHaveCount(1);

    $slotTimes = collect($todaySlots['slots'])->map(function ($slot) {
        return [
            'from' => Carbon::parse($slot['from'])->format('H:i'),
            'to' => Carbon::parse($slot['to'])->format('H:i'),
        ];
    })->toArray();

    // Only the slot ending in 2 hours should be present
    expect($slotTimes)->not->toContain([
        'from' => '02:00',
        'to' => '03:30',
    ]);

    expect($slotTimes)->toContain([
        'from' => '03:00',
        'to' => '05:00',
    ]);
});

test('availableSlots includes future days correctly', function () {
    // Set a fixed time for testing
    Carbon::setTestNow(Carbon::parse('2025-01-15 14:00:00'));

    /** @var Deal $deal */
    $deal = Deal::factory()->create([
        'max_usage_per_day' => 10,
    ]);

    $today = Carbon::now()->format('w'); // 3 for Wednesday
    $tomorrow = Carbon::now()->addDay()->format('w'); // 4 for Thursday

    // Slot for today that ends in 30 minutes (should be filtered out)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '13:45',
        'to' => '14:30', // ends in 30 minutes
    ]);

    // Slot for tomorrow that ends in 2 hours (should be included)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $tomorrow,
        'from' => '14:30',
        'to' => '16:00', // ends in 2 hours (future day)
    ]);

    $availableSlots = $deal->available_slots;

    // Should only have slots for tomorrow (today should be filtered out because it has no valid slots)
    expect($availableSlots)->toHaveCount(1);

    // Today should not be present (filtered out because it has no valid slots)
    expect($availableSlots)->not->toHaveKey($today);

    // Tomorrow should have 1 slot (not filtered because it's in the future)
    expect($availableSlots)->toHaveKey($tomorrow);
    expect($availableSlots[$tomorrow]['slots'])->toHaveCount(1);
});

test('availableSlots filters out dates with no slots', function () {
    // Set a fixed time for testing
    Carbon::setTestNow(Carbon::parse('2025-01-15 14:00:00'));

    /** @var Deal $deal */
    $deal = Deal::factory()->create([
        'max_usage_per_day' => 10,
    ]);

    $today = Carbon::now()->format('w'); // 3 for Wednesday
    $tomorrow = Carbon::now()->addDay()->format('w'); // 4 for Thursday

    // Today: only past slots (should be filtered out, resulting in no slots for today)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '10:00',
        'to' => '12:00', // 2 hours ago
    ]);

    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $today,
        'from' => '12:30',
        'to' => '13:30', // 30 minutes ago
    ]);

    // Tomorrow: has future slots (should be included)
    DealSlot::factory()->create([
        'deal_id' => $deal->id,
        'day' => $tomorrow,
        'from' => '14:30',
        'to' => '16:00', // ends in 2 hours (future day)
    ]);

    $availableSlots = $deal->available_slots;

    // Should only have slots for tomorrow (today should be filtered out because it has no valid slots)
    expect($availableSlots)->toHaveCount(1);

    // Today should not be present
    expect($availableSlots)->not->toHaveKey($today);

    // Tomorrow should be present with 1 slot
    expect($availableSlots)->toHaveKey($tomorrow);
    expect($availableSlots[$tomorrow]['slots'])->toHaveCount(1);
});
