<?php

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\Tag;
use App\Models\User;
use App\Models\UserDeal;
use Illuminate\Support\Collection;

use function Pest\Laravel\actingAs;

test('deal snapshot is stored when reserving a deal', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user);

    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state(fn (array $attributes) => [
            'from' => '14:00',
            'to' => '18:00',
            'day' => now()->format('w'),
        ]))
        ->create([
            'title' => 'Test Deal',
            'description' => 'Test Description',
            'max_saving' => 50.00,
            'max_usage_per_day' => 10,
            'reuse_limit_days' => 7,
        ]);

    /** @var Collection<int, Tag> $tags */
    $tags = Tag::factory()->category()->count(2)->create();
    $deal->service_options()->sync($tags->pluck('id'));
    $deal = $deal->fresh('service_options');

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserved_at' => now(),
        'status' => 'upcoming',
        'reserve_slot' => [
            'date' => now()->addDay()->format('Y-m-d'),
            'slot' => [
                'from' => '14:00',
                'to' => '18:00',
            ],
        ],
        'reuse_after' => now()->addDays(7),
    ]);

    /** @var \App\Enums\DealType $deaType */
    $deaType = $deal->deal_type;

    /** @var Collection<?Tag> $serviceTypes */
    $serviceTypes = $deal->service_options;

    // Manually set the deal snapshot
    $userDeal->deal_snapshot = [
        'title' => $deal->title,
        'description' => $deal->description,
        'deal_type' => $deaType->name,
        'service_options' => $serviceTypes->map(fn (Tag $tag) => [
            'id' => $tag->id,
            'title' => $tag->title,
        ])->toArray(),
        'max_saving' => $deal->max_saving,
        'max_usage_per_day' => $deal->max_usage_per_day,
        'reuse_limit_days' => $deal->reuse_limit_days,
    ];
    $userDeal->save();

    expect($userDeal->deal_snapshot)->toBeArray()
        ->and($userDeal->deal_snapshot['title'])->toBe('Test Deal')
        ->and($userDeal->deal_snapshot['description'])->toBe('Test Description')
        ->and($userDeal->deal_snapshot['max_saving'])->toEqual(50.00)
        ->and($userDeal->deal_snapshot['max_usage_per_day'])->toBe(10)
        ->and($userDeal->deal_snapshot['reuse_limit_days'])->toBe(7)
        ->and($userDeal->deal_snapshot['service_options'])->toBeArray()
        ->and(count($userDeal->deal_snapshot['service_options']))->toBe(2);
});

test('deal snapshot remains unchanged when deal is updated', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user);

    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state(fn (array $attributes) => [
            'from' => '14:00',
            'to' => '18:00',
            'day' => now()->addDay()->format('w'),
        ]))
        ->create([
            'title' => 'Original Title',
            'description' => 'Original Description',
            'max_usage_per_day' => 10,
        ]);

    /** @var User $user */
    $user = User::factory()->create();

    actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal(
                    input: {id: $id, reserve_slot: {date: $date, slot: {from: $from, to: $to}}, myDealIdToRenew: null}
                ) {
                    myDeal {
                        id
                        status
                    }
                }
            }',
        [
            'id' => $deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 14:00'),
            'to' => now()->addDay()->format('Y-m-d 18:00'),
        ])->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id' => UserDeal::latest('id')->value('id'),
                        'status' => 'UPCOMING',
                    ],
                ],
            ],
        ]);

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::query()->latest('id')->first();

    $originalSnapshot = $userDeal->deal_snapshot;

    // Update the deal
    $deal->update([
        'title' => 'Updated Title',
        'description' => 'Updated Description',
    ]);

    $userDeal->refresh();

    expect($userDeal->deal_snapshot)->toBe($originalSnapshot)
        ->and($userDeal->deal_snapshot['title'])->toBe('Original Title')
        ->and($userDeal->deal_snapshot['description'])->toBe('Original Description');
});
