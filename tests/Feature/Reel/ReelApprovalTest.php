<?php

use App\Filament\Resources\ReelResource\Pages\EditReel;
use App\Models\Admin;
use App\Models\Creator;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;

beforeEach(function () {
    Storage::fake('local');

    // Set application key for encryption
    config(['app.key' => 'base64:'.base64_encode(random_bytes(32))]);
});

it('allows admin to approve a reel', function () {
    // Create an admin
    /** @var Admin $admin */
    $admin = Admin::factory()->create();

    // Create a partner and location
    /** @var Partner $partner */
    $partner = Partner::factory()->create();
    /** @var PartnerLocation $location */
    $location = PartnerLocation::factory()->create(['partner_id' => $partner->id]);

    // Create a creator
    /** @var Creator $creator */
    $creator = Creator::factory()->create();

    // Create a reel with pending status
    $videoFile = UploadedFile::fake()->create('test-video.mp4', 1024);
    Storage::put('reels/test-video.mp4', $videoFile->getContent());

    /** @var Reel $reel */
    $reel = Reel::create([
        'caption' => 'Test reel caption',
        'url' => 'reels/test-video.mp4',
        'partner_id' => $partner->id,
        'creatable_type' => Creator::class,
        'creatable_id' => $creator->id,
        'approval_status' => 'pending',
    ]);

    $reel->locations()->attach($location->id);

    // Login as admin
    /** @var \Illuminate\Contracts\Auth\Authenticatable $admin */
    actingAs($admin, 'admin');

    // Update the reel to approve it
    /** @var mixed $livewireComponent */
    $livewireComponent = \Pest\Livewire\livewire(EditReel::class, ['record' => $reel->id]);
    $livewireComponent->fillForm([
        'approval_status' => 'approved',
    ]);
    $livewireComponent->call('save');
    $livewireComponent->assertHasNoFormErrors();

    // Check that the reel was approved
    assertDatabaseHas('reels', [
        'id' => $reel->id,
        'approval_status' => 'approved',
    ]);
});

it('allows admin to reject a reel', function () {
    // Create an admin
    /** @var Admin $admin */
    $admin = Admin::factory()->create();

    // Create a partner and location
    /** @var Partner $partner */
    $partner = Partner::factory()->create();
    /** @var PartnerLocation $location */
    $location = PartnerLocation::factory()->create(['partner_id' => $partner->id]);

    // Create a creator
    /** @var Creator $creator */
    $creator = Creator::factory()->create();

    // Create a reel with pending status
    $videoFile = UploadedFile::fake()->create('test-video.mp4', 1024);
    Storage::put('reels/test-video.mp4', $videoFile->getContent());

    /** @var Reel $reel */
    $reel = Reel::create([
        'caption' => 'Test reel caption',
        'url' => 'reels/test-video.mp4',
        'partner_id' => $partner->id,
        'creatable_type' => Creator::class,
        'creatable_id' => $creator->id,
        'approval_status' => 'pending',
    ]);

    $reel->locations()->attach($location->id);

    // Login as admin
    /** @var \Illuminate\Contracts\Auth\Authenticatable $admin */
    actingAs($admin, 'admin');

    // Update the reel to reject it
    /** @var mixed $livewireComponent */
    $livewireComponent = \Pest\Livewire\livewire(EditReel::class, ['record' => $reel->id]);
    $livewireComponent->fillForm([
        'approval_status' => 'rejected',
    ]);
    $livewireComponent->call('save');
    $livewireComponent->assertHasNoFormErrors();

    // Check that the reel was rejected
    assertDatabaseHas('reels', [
        'id' => $reel->id,
        'approval_status' => 'rejected',
    ]);
});

it('only indexes approved reels in algolia', function () {
    // Create a partner and location
    /** @var Partner $partner */
    $partner = Partner::factory()->create();
    /** @var PartnerLocation $location */
    $location = PartnerLocation::factory()->create(['partner_id' => $partner->id]);

    // Create deals for the location (at least 2 deals are required for shouldBeSearchable)
    /** @var \Illuminate\Database\Eloquent\Collection $deals */
    $deals = \App\Models\Deal::factory(2)->create([
        'partner_location_id' => $location->id,
    ]);

    // Create a creator
    /** @var Creator $creator */
    $creator = Creator::factory()->create();

    // Create a reel with pending status
    /** @var Reel $pendingReel */
    $pendingReel = Reel::create([
        'caption' => 'Pending reel',
        'url' => 'reels/pending.mp4',
        'partner_id' => $partner->id,
        'creatable_type' => Creator::class,
        'creatable_id' => $creator->id,
        'approval_status' => 'pending',
    ]);

    $pendingReel->locations()->attach($location->id);

    // Create a reel with approved status
    /** @var Reel $approvedReel */
    $approvedReel = Reel::create([
        'caption' => 'Approved reel',
        'url' => 'reels/approved.mp4',
        'partner_id' => $partner->id,
        'creatable_type' => Creator::class,
        'creatable_id' => $creator->id,
        'approval_status' => 'approved',
    ]);

    $approvedReel->locations()->attach($location->id);

    // Create a reel with rejected status
    /** @var Reel $rejectedReel */
    $rejectedReel = Reel::create([
        'caption' => 'Rejected reel',
        'url' => 'reels/rejected.mp4',
        'partner_id' => $partner->id,
        'creatable_type' => Creator::class,
        'creatable_id' => $creator->id,
        'approval_status' => 'rejected',
    ]);

    $rejectedReel->locations()->attach($location->id);

    $approvedReel->refresh();
    // Check that only approved reels should be searchable
    expect($pendingReel->shouldBeSearchable())->toBeFalse();
    expect($approvedReel->shouldBeSearchable())->toBeTrue();
    expect($rejectedReel->shouldBeSearchable())->toBeFalse();
});
