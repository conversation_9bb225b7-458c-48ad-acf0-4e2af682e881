<?php

declare(strict_types=1);

namespace App\GraphQL\Queries\Review;

use App\Models\Review;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class UserReviewForDealQuery
{
    public function __invoke(null $_, array $args, GraphQLContext $context): ?Review
    {
        $dealId = $args['deal_id'];
        $userId = $context->user()->id;

        return Review::where('user_id', $userId)
            ->where('deal_id', $dealId)
            ->with(['user', 'deal', 'partnerLocation'])
            ->first();
    }
}
