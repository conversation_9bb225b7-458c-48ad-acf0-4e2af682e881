<?php

declare(strict_types=1);

namespace App\GraphQL\Queries\Review;

use App\Models\Review;
use Illuminate\Database\Eloquent\Builder;

final readonly class ReviewsByDealQuery
{
    public function __invoke(null $_, array $args): mixed
    {
        $dealId = $args['deal_id'];
        $filters = $args['filters'] ?? [];
        $sortBy = $args['sort_by'] ?? 'CREATED_AT';
        $sortOrder = $args['sort_order'] ?? 'DESC';
        $first = $args['first'] ?? 20;
        $page = $args['page'] ?? 1;

        $query = Review::where('deal_id', $dealId)
            ->with(['user', 'deal', 'partnerLocation']);

        // Apply filters
        $this->applyFilters($query, $filters);

        // Apply sorting
        $this->applySorting($query, $sortBy, $sortOrder);

        return $query->paginate($first, ['*'], 'page', $page);
    }

    private function applyFilters(Builder $query, array $filters): void
    {
        if (isset($filters['rating'])) {
            $query->where('rating', $filters['rating']);
        }

        if (isset($filters['min_rating'])) {
            $query->where('rating', '>=', $filters['min_rating']);
        }

        if (isset($filters['would_visit_again'])) {
            $query->where('would_visit_again', $filters['would_visit_again']);
        }

        if (isset($filters['has_review_text'])) {
            if ($filters['has_review_text']) {
                $query->whereNotNull('review_text');
            } else {
                $query->whereNull('review_text');
            }
        }
    }

    private function applySorting(Builder $query, string $sortBy, string $sortOrder): void
    {
        $direction = strtolower($sortOrder) === 'asc' ? 'asc' : 'desc';

        switch ($sortBy) {
            case 'RATING':
                $query->orderBy('rating', $direction);
                break;
            case 'SAVINGS_AMOUNT':
                $query->orderBy('savings_amount', $direction);
                break;
            case 'CREATED_AT':
            default:
                $query->orderBy('created_at', $direction);
                break;
        }
    }
}
