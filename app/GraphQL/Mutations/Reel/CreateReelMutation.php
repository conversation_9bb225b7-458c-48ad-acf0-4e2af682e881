<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Reel;

use App\Jobs\ExtractThumbnailFromReelJob;
use App\Models\Creator;
use App\Models\PartnerLocation;
use App\Models\Reel;
use App\Models\User;
use Livewire\Features\SupportFileUploads\FileUploadConfiguration;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class CreateReelMutation
{
    /**
     * @return array{reel: Reel}
     */
    public function __invoke(null $_, array $args, GraphQLContext $context): array
    {
        /** @var User $user */
        $user = $context->user();

        /** @var PartnerLocation|null $partnerLocation */
        $partnerLocation = PartnerLocation::find($args['input']['partner_location_id']);

        if (! $partnerLocation) {
            throw new \Exception('The selected partner location id is invalid');
        }

        // User is creating the reel
        /** @var Creator|null $creator */
        $creator = $user->creator;

        if (! $creator) {
            throw new \Exception('you are not a creator');
        }

        $reelData = $args['input'];
        $originalUrl = $reelData['url'];

        // Extract the filename from the URL
        // The URL format is typically something like: livewire-tmp/filename.ext
        $path = parse_url($originalUrl, PHP_URL_PATH);
        $filename = basename($path);

        // Check if the file exists in the livewire-tmp directory
        $tempFilePath = FileUploadConfiguration::path($filename);
        $storage = FileUploadConfiguration::storage();

        if ($storage->exists($tempFilePath)) {
            // Create a new path for the file in the root of the bucket
            $newFilename = '/'.uniqid().'-'.$filename;

            // Copy the file to the new location with public visibility
            $storage->copy($tempFilePath, $newFilename);
            $storage->setVisibility($newFilename, 'public');

            // Get the public URL of the file
            $publicUrl = $storage->url($newFilename);

            // Delete the temporary file
            $storage->delete($tempFilePath);

            // Update the URL to the public URL
            $reelData['url'] = $newFilename;
        }

        /** @var Reel $reel */
        $reel = Reel::query()->create([
            'caption' => $reelData['caption'] ?? null,
            'url' => $reelData['url'],
            'partner_id' => $partnerLocation->partner->id ?? null,
            'thumbnail' => $reelData['thumbnail'] ?? null,
            'creatable_type' => Creator::class,
            'creatable_id' => $creator->id,
            'approval_status' => 'pending',
        ]);

        $reel->locations()->attach($partnerLocation->id);

        if (! isset($reelData['thumbnail'])) {
            ExtractThumbnailFromReelJob::dispatch($reel);
        }

        return [
            'reel' => $reel,
        ];
    }
}
