<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Reel;

use App\Models\User;
use Illuminate\Http\UploadedFile;
use Livewire\Features\SupportFileUploads\GenerateSignedUploadUrl;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class GetReelUploadUrlMutation
{
    public function __invoke(null $_, array $args, GraphQLContext $context)
    {
        // Get the authenticated user
        /** @var User $user */
        $user = $context->user();

        $filename = $args['input']['filename'];
        $contentType = $args['input']['contentType'];

        // Generate a unique key for the file
        $key = 'reels/'.$user->id.'/'.uniqid().'-'.$filename;

        $file = UploadedFile::fake()->create($filename, 2048, $contentType);

        // In test environment, return mock data
        if (app()->environment('testing')) {
            $mockPath = 'reels/'.$user->id.'/'.uniqid().'-'.$filename;
            $mockUrl = 'https://example.com/'.$mockPath;
            $mockHeaders = [
                [
                    'key' => 'Content-Type',
                    'value' => $contentType ?: 'application/octet-stream',
                ],
                [
                    'key' => 'Authorization',
                    'value' => 'mock-auth-token',
                ],
            ];

            return [
                'path' => $mockPath,
                'url' => $mockUrl,
                'headers' => $mockHeaders,
            ];
        }

        // For production, use the real S3 client
        $generator = new GenerateSignedUploadUrl;
        $result = $generator->forS3($file);

        // Transform headers from associative array to array of Header objects
        $headers = [];
        foreach ($result['headers'] as $key => $value) {
            $headers[] = [
                'key' => $key,
                'value' => is_array($value) ? json_encode($value) : $value,
            ];
        }

        $result['headers'] = $headers;

        return $result;
    }
}
