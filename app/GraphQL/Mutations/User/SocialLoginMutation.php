<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Actions\SocialLogin;

final readonly class SocialLoginMutation
{
    public function __invoke(null $_, array $args)
    {
        $provider = $args['provider'];
        $token = $args['token'];
        $firstName = $args['firstName'] ?? null;
        $lastName = $args['lastName'] ?? null;

        return SocialLogin::execute($provider, $token, $firstName, $lastName);
    }
}
