<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Review;

use App\Actions\Review\UpdateReviewAction;
use App\Models\User;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class UpdateReviewMutation
{
    public function __invoke(null $_, array $args, GraphQLContext $context): array
    {
        /** @var User $user */
        $user = $context->user();

        $review = UpdateReviewAction::handle($user, $args['input']);

        return [
            'review' => $review,
            'message' => 'Review updated successfully!',
        ];
    }
}
