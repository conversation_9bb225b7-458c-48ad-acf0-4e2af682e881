<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Review;

use App\Actions\Review\SubmitReviewAction;
use App\Models\User;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class SubmitReviewMutation
{
    public function __invoke(null $_, array $args, GraphQLContext $context): array
    {
        /** @var User $user */
        $user = $context->user();

        $review = SubmitReviewAction::handle($user, $args['input']);

        return [
            'review' => $review,
            'message' => 'Review submitted successfully!',
        ];
    }
}
