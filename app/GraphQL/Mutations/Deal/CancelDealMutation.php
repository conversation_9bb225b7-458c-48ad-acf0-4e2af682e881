<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Deal;

use App\Actions\Deal\CancelDeal;

final readonly class CancelDealMutation
{
    public function __invoke(null $_, array $args)
    {
        try {
            $message = CancelDeal::handle($args['input']['myDealId']);
        } catch (\Throwable $e) {
            report($e);

            throw $e;
        }

        return [
            'message' => $message,
        ];
    }
}
