<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Deal;

use App\Actions\Deal\RedeemDeal;

final readonly class RedeemDealMutation
{
    public function __invoke(null $_, array $args)
    {
        try {
            $myDeal = RedeemDeal::handle($args['input']['myDealId']);
        } catch (\Throwable $e) {
            report($e);

            throw $e;
        }

        return [
            'myDeal' => $myDeal,
        ];
    }
}
