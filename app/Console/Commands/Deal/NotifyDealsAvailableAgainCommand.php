<?php

namespace App\Console\Commands\Deal;

use App\Models\Deal;
use App\Models\User;
use App\Models\UserDeal;
use App\Notifications\DealIsReservableNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;

class NotifyDealsAvailableAgainCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deals:notify-available-again';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send notifications for deals that are available again after the reuse_after period';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $yesterday = Carbon::yesterday()->toDateString();

        // Find all user deals where reuse_after was yesterday
        $userDeals = UserDeal::query()
            ->whereDate('reuse_after', $yesterday)
            ->whereHas('user')
            ->whereHas('deal', function ($query) {
                // Ensure the deal is still active and available
                $query->whereNull('deleted_at');
            })
            ->with(['user', 'deal.partner_place.partner'])
            ->get();

        $this->info("Found {$userDeals->count()} deals that are available again.");

        $notificationsSent = 0;

        /** @var UserDeal $userDeal */
        foreach ($userDeals as $userDeal) {
            // Skip if user doesn't have an FCM token (notifications disabled)

            /** @var User $user */
            $user = $userDeal->user;

            /** @var Deal $deal */
            $deal = $userDeal->deal;

            if (! $user->fcm_token) {
                $this->line("Skipping notification for user {$user->id} - no FCM token");

                continue;
            }

            // Send notification
            $user->notify(new DealIsReservableNotification($userDeal));
            $notificationsSent++;

            $this->line("Sent notification for deal {$deal->id} to user {$user->id}");
        }

        $this->info("Sent {$notificationsSent} notifications.");

        return self::SUCCESS;
    }
}
