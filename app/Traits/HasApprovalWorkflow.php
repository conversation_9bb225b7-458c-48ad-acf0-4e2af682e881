<?php

namespace App\Traits;

use App\Enums\ApprovalStatus;
use App\Models\Admin;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HasApprovalWorkflow
{
    /**
     * Initialize the trait
     */
    public function initializeHasApprovalWorkflow(): void
    {
        // Add approval workflow fields to fillable
        $this->fillable = array_merge($this->fillable, [
            'approval_status',
            'pending_data',
            'submitted_by',
            'submitted_at',
            'reviewed_by',
            'reviewed_at',
            'rejection_reason',
        ]);

        // Add approval workflow casts
        $this->casts = array_merge($this->casts, [
            'approval_status' => ApprovalStatus::class,
            'pending_data' => 'array',
            'submitted_at' => 'datetime',
            'reviewed_at' => 'datetime',
        ]);
    }

    /**
     * Get the employee who submitted the changes
     */
    public function submittedBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'submitted_by');
    }

    /**
     * Get the admin who reviewed the changes
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'reviewed_by');
    }

    /**
     * Check if the record is pending approval
     */
    public function isPending(): bool
    {
        return $this->approval_status === ApprovalStatus::PENDING;
    }

    /**
     * Check if the record is approved
     */
    public function isApproved(): bool
    {
        return $this->approval_status === ApprovalStatus::APPROVED;
    }

    /**
     * Check if the record is rejected
     */
    public function isRejected(): bool
    {
        return $this->approval_status === ApprovalStatus::REJECTED;
    }

    /**
     * Check if the record is in draft status
     */
    public function isDraft(): bool
    {
        return $this->approval_status === ApprovalStatus::DRAFT;
    }

    /**
     * Submit for approval
     */
    public function submitForApproval(array $data, int $employeeId): void
    {
        $this->update([
            'approval_status' => ApprovalStatus::PENDING,
            'pending_data' => $data,
            'submitted_by' => $employeeId,
            'submitted_at' => now(),
            'reviewed_by' => null,
            'reviewed_at' => null,
            'rejection_reason' => null,
        ]);
    }

    /**
     * Approve the changes
     */
    public function approve(int $adminId): void
    {
        if ($this->pending_data === null || !is_array($this->pending_data)) {
            // No pending changes to approve, just update status
            $this->update([
                'approval_status' => ApprovalStatus::APPROVED,
                'reviewed_by' => $adminId,
                'reviewed_at' => now(),
                'rejection_reason' => null,
            ]);
            return;
        }

        $pendingData = $this->pending_data;

        // Handle special fields that need separate processing
        $specialFields = ['tags', 'slots', 'slot'];
        $relationshipData = [];

        foreach ($specialFields as $field) {
            if (isset($pendingData[$field])) {
                $relationshipData[$field] = $pendingData[$field];
                unset($pendingData[$field]);
            }
        }

        // Update the main model fields individually to avoid conflicts
        foreach ($pendingData as $field => $value) {
            $this->{$field} = $value;
        }

        // Update approval status fields
        $this->approval_status = ApprovalStatus::APPROVED;
        $this->pending_data = null;
        $this->reviewed_by = $adminId;
        $this->reviewed_at = now();
        $this->rejection_reason = null;

        $this->save();

        // Handle relationship updates
        $this->handleRelationshipUpdates($relationshipData);
    }

    /**
     * Handle updates to relationships
     */
    protected function handleRelationshipUpdates(array $relationshipData): void
    {
        // Handle tags for PartnerLocation
        if (isset($relationshipData['tags']) && method_exists($this, 'tags')) {
            $this->tags()->sync($relationshipData['tags']);
        }

        // Handle deal slots for Deal
        if ((isset($relationshipData['slots']) || isset($relationshipData['slot'])) && method_exists($this, 'dealSlots')) {
            $slotsData = $relationshipData['slots'] ?? $relationshipData['slot'] ?? [];

            // Delete existing slots
            $this->dealSlots()->delete();

            // Create new slots
            foreach ($slotsData as $slotData) {
                if (isset($slotData['day'], $slotData['from'], $slotData['to'])) {
                    $this->dealSlots()->create([
                        'day' => $slotData['day'],
                        'from' => $slotData['from'],
                        'to' => $slotData['to'],
                    ]);
                }
            }
        }
    }

    /**
     * Reject the changes
     */
    public function reject(int $adminId, ?string $reason = null): void
    {
        $this->update([
            'approval_status' => ApprovalStatus::REJECTED,
            'reviewed_by' => $adminId,
            'reviewed_at' => now(),
            'rejection_reason' => $reason,
        ]);
    }

    /**
     * Withdraw the submission
     */
    public function withdraw(): void
    {
        $this->update([
            'approval_status' => ApprovalStatus::DRAFT,
            'pending_data' => null,
            'submitted_by' => null,
            'submitted_at' => null,
            'reviewed_by' => null,
            'reviewed_at' => null,
            'rejection_reason' => null,
        ]);
    }

    /**
     * Get the current data (either approved or pending)
     */
    public function getCurrentData(): array
    {
        if ($this->isPending() && $this->pending_data !== null && is_array($this->pending_data)) {
            return array_merge($this->getAttributes(), $this->pending_data);
        }

        return $this->getAttributes();
    }

    /**
     * Get the original approved data
     */
    public function getOriginalData(): array
    {
        $attributes = $this->getAttributes();

        // Remove all approval-related fields to get the original data
        unset(
            $attributes['pending_data'],
            $attributes['approval_status'],
            $attributes['submitted_by'],
            $attributes['submitted_at'],
            $attributes['reviewed_by'],
            $attributes['reviewed_at'],
            $attributes['rejection_reason']
        );

        return $attributes;
    }

    /**
     * Check if the record can be edited
     */
    public function canBeEdited(): bool
    {
        return ! $this->isPending();
    }

    /**
     * Scope to get only approved records
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', ApprovalStatus::APPROVED);
    }

    /**
     * Scope to get only pending records
     */
    public function scopePending($query)
    {
        return $query->where('approval_status', ApprovalStatus::PENDING);
    }

    /**
     * Scope to get only rejected records
     */
    public function scopeRejected($query)
    {
        return $query->where('approval_status', ApprovalStatus::REJECTED);
    }

    /**
     * Scope to get only draft records
     */
    public function scopeDraft($query)
    {
        return $query->where('approval_status', ApprovalStatus::DRAFT);
    }
}
