<?php

namespace App\Models;

use App\Contracts\CanBeAddedToCollection;
use App\Contracts\CanUploadReel;
use App\Contracts\Likeable;
use App\Traits\HasCollectionItems;
use App\Traits\HasLikes;
use Database\Factories\CreatorFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/** @property Media $avatar */
class Creator extends Model implements CanBeAddedToCollection, CanUploadReel, HasMedia, Likeable
{
    /** @use HasFactory<CreatorFactory> */
    use HasCollectionItems, HasFactory, HasLikes, InteractsWithMedia, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'deleted_at' => 'datetime', // Add this cast
    ];

    protected static function booted(): void
    {
        static::forceDeleting(function (Creator $creator) {
            /** @var Reel $reel */
            foreach ($creator->reels as $reel) {
                $reel->locations()->detach(); // No longer passing $reel->id as detach() without arguments removes all related records.
            }
        });

        static::deleting(function (Creator $creator) {
            /** @var Reel $reel */
            foreach ($creator->reels as $reel) {
                $reel->delete();
            }
            CollectionItem::where('collectable_type', "App\Models\Creator")->where('collectable_id', $creator->id)->delete();
        });

        static::updated(function (Creator $creator) {
            /** @var Reel $reel */
            foreach ($creator->reels as $reel) {
                $reel->touch();
            }
        });

        static::saved(function (Creator $creator) {
            /** @var Reel $reel */
            foreach ($creator->reels as $reel) {
                $reel->touch();
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function following_users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_follow_creator')
            ->as('users');
    }

    public function reels(): MorphMany
    {
        return $this->morphMany(Reel::class, 'creatable');
    }

    public function avatar(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')
            ->where('collection_name', 'avatar');
    }

    #[\Deprecated('Use formatted_name property instead')]
    public function getName(): string
    {
        return $this->name;
    }

    public function toSearchableArray(): array
    {
        /** @var Media|null $avatar */
        $avatar = $this->avatar;

        return [
            'id' => (string) $this->id,
            'name' => $this->getName(),
            'formatted_name' => $this->formatted_name,
            'avatar' => $avatar?->full_url,
        ];
    }
}
