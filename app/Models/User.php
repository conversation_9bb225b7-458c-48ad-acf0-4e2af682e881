<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'password',
        'provider',
        'provider_id',
        'fcm_token',
        'max_allowed_invitations',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'deleted_at' => 'datetime',
        ];
    }

    public function deals(): BelongsToMany
    {
        return $this->belongsToMany(Deal::class, 'user_deal')
            ->as('deal')
            ->withPivot([
                'reserved_at',
                'redeemed_at',
                'reuse_after',
                'status',
                'reserve_slot',
                'id',
            ])
            ->using(UserDeal::class);
    }

    public function following_creators(): BelongsToMany
    {
        return $this->belongsToMany(Creator::class, 'user_follow_creator')
            ->as('creator');
    }

    public function following_places(): BelongsToMany
    {
        return $this->belongsToMany(PartnerLocation::class, 'user_follow_place', 'user_id', 'place_id');
    }

    public function likes(): HasMany
    {
        return $this->hasMany(Like::class);
    }

    public function creator(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Creator::class);
    }

    public function collections(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Collection::class, 'collection_user')
            ->withPivot('role')
            ->withTimestamps();
    }

    public function ownedCollections(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Collection::class, 'user_id');
    }

    public function sentInvitations(): HasMany
    {
        return $this->hasMany(Invitation::class, 'inviter_user_id');
    }

    public function receivedInvitation(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Invitation::class, 'invitee_user_id');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    public function getInvitationInfo(): array
    {
        $total = $this->max_allowed_invitations;
        $used = $this->sentInvitations()->whereHas('invitee')->count();

        return [
            'total' => $total,
            'remaining' => max(0, $total - $used),
        ];
    }

    public function routeNotificationForFcm(): ?string
    {
        return $this->fcm_token;
    }
}
