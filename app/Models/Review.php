<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Review extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'deal_id',
        'partner_location_id',
        'rating',
        'savings_amount',
        'would_visit_again',
        'review_text',
    ];

    protected $casts = [
        'rating' => 'integer',
        'savings_amount' => 'decimal:2',
        'would_visit_again' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user who wrote this review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the deal this review is for.
     */
    public function deal(): BelongsTo
    {
        return $this->belongsTo(Deal::class);
    }

    /**
     * Get the partner location this review is for.
     */
    public function partnerLocation(): BelongsTo
    {
        return $this->belongsTo(PartnerLocation::class, 'partner_location_id');
    }

    /**
     * Scope to filter reviews by rating.
     */
    public function scopeWithRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to filter reviews by minimum rating.
     */
    public function scopeWithMinRating($query, int $minRating)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Scope to filter reviews that would visit again.
     */
    public function scopeWouldVisitAgain($query)
    {
        return $query->where('would_visit_again', true);
    }

    /**
     * Scope to order by most recent.
     */
    public function scopeRecent($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Scope to order by highest rating.
     */
    public function scopeHighestRated($query)
    {
        return $query->orderBy('rating', 'desc');
    }
}
