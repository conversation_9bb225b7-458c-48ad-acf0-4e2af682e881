<?php

namespace App\Models;

use App\Contracts\CanBeAddedToCollection;
use App\Contracts\LightHouseCachable;
use App\Contracts\Likeable;
use App\Filament\Resources\PartnerPlaceResource;
use App\Traits\HasCollectionItems;
use App\Traits\HasLightHouseCache;
use App\Traits\HasLikes;
use Carbon\Carbon;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property Collection $images
 * @property Collection $location
 * @property Collection $opening_hours
 * @property Media $avatar
 * @property array $rates
 * @property Collection $meal_times
 * @property Collection $service_options
 * @property Collection $dietary
 * @property Collection $cuisine_types
 * @property Collection $ambiance
 * @property Collection $specialities
 * @property Collection $parking
 * @property Collection $cravings
 * @property Collection $area
 * @property Collection $retail_destination
 * @property Collection $retailDestination
 */
class PartnerLocation extends Model implements CanBeAddedToCollection, HasMedia, LightHouseCachable, Likeable
{
    use HasCollectionItems, HasFactory, HasLightHouseCache, HasLikes, InteractsWithMedia,SoftDeletes;

    protected static function booted(): void
    {
        static::deleting(function (PartnerLocation $partnerLocation) {
            // Delete associated reels
            /** @var \Illuminate\Database\Eloquent\Collection<int, Reel> $reels */
            $reels = $partnerLocation->reels()->get();
            foreach ($reels as $reel) {
                $reel->delete();
            }

            // Delete associated deals (soft delete if Deal model uses SoftDeletes)
            /** @var \Illuminate\Database\Eloquent\Collection<int, Deal> $deals */
            $deals = $partnerLocation->deals()->get();
            foreach ($deals as $deal) {
                $deal->delete();
            }

            // Delete associated collection items
            CollectionItem::where('collectable_type', "App\Models\PartnerLocation")->where('collectable_id', $partnerLocation->id)->delete();
        });
        static::forceDeleting(function (PartnerLocation $partnerLocation) {
            // Detach associated employees (removes records from pivot table)
            $partnerLocation->employees()->detach();
        });
        static::saved(function (PartnerLocation $partnerLocation) {
            foreach ($partnerLocation->reels as $reel) {
                /** @var Reel $reel */
                $reel->touch();
            }
        });

        static::updated(function (PartnerLocation $partnerLocation) {
            $partnerLocation->clearLightHouseCaches();
            foreach ($partnerLocation->reels as $reel) {
                /** @var Reel $reel */
                $reel->touch();
            }
        });
    }

    protected $guarded = [];

    public function getCacheKeys(): array
    {
        return [
            'partnerPlace:id',
        ];
    }

    protected function casts(): array
    {
        return [
            'opening_hours' => 'collection',
            'deleted_at' => 'datetime',
            'is_searchable' => 'boolean',
        ];
    }

    protected function openingHours(): Attribute
    {
        return Attribute::make(
            get: function ($value, array $attributes) {
                $collection = collect(json_decode($attributes['opening_hours'] ?? '[]', true));

                return $collection->map(function ($slot) {
                    $day = $slot['day'];
                    $now = Carbon::now();
                    $date = $now->dayOfWeek === $day ? $now->toDateString() : $now->next($day)->toDateString();

                    return [
                        'day' => $slot['day'],
                        'from' => Carbon::parse($date.' '.$slot['from'])->toDateTimeString(),
                        'to' => $slot['to'] < $slot['from'] ? Carbon::parse($date.' '.$slot['to'])->addDay()->toDateTimeString() : Carbon::parse($date.' '.$slot['to'])->toDateTimeString(),
                    ];
                });
            },
        );
    }

    public function getScoutKey(): string
    {
        return (string) $this->getKey();
    }

    public function toSearchableArray(): array
    {
        $deals = $this->deals;
        $data = [
            'id' => (string) $this->id,
            'name' => $this->name,
        ];

        if ($deals->isNotEmpty()) {
            /** @phpstan-ignore-next-line */
            $data['deals'] = $deals->map(fn (Deal $deal) => $deal->toSearchableArray());
        }

        if ($this->rates) {
            $data['rates'] = $this->rates;
        }

        if ($this->hasMedia('avatar')) {
            $data['avatar'] = $this->avatar->getFullUrl();
        }

        if ($this->price_per_person) {
            $data['price_per_person'] = (float) $this->price_per_person;
        }

        // List of optional fields to check
        $optionalCollectionFields = [
            \App\Enums\TagType::MEAL_TIMES->value => $this->meal_times,
            \App\Enums\TagType::SERVICE_OPTIONS->value => $this->service_options,
            \App\Enums\TagType::DIETARY->value => $this->dietary,
            \App\Enums\TagType::CUISINE_TYPES->value => $this->cuisine_types,
            \App\Enums\TagType::AMBIANCE->value => $this->ambiance,
            \App\Enums\TagType::SPECIALITIES->value => $this->specialities,
            \App\Enums\TagType::PARKING->value => $this->parking,
            \App\Enums\TagType::CRAVINGS->value => $this->cravings,
        ];

        $optionalSingularCollectionFields = [
            \App\Enums\TagType::AREA->value => $this->area,
            \App\Enums\TagType::RETAIL_DESTINATION->value => $this->retail_destination,
        ];

        if ($this->opening_hours->isNotEmpty()) {
            $data['opening_hours'] = $this->opening_hours;
        }

        foreach ($optionalCollectionFields as $key => $collection) {
            /** @var Collection $collection */
            if ($collection->isNotEmpty()) {
                $data[$key] = $collection->pluck('title')->toArray();
            }
        }

        foreach ($optionalSingularCollectionFields as $key => $singularCollectionField) {
            /** @var Collection $singularCollectionField */
            if ($singularCollectionField->isNotEmpty()) {
                $data[$key] = $singularCollectionField->pluck('title')->implode(',');
            }
        }

        return $data;
    }

    protected function location(): Attribute
    {
        return Attribute::make(
            get: fn ($value, array $attributes) => [
                'lat' => (float) $attributes['lat'],
                'lng' => (float) $attributes['lng'],
            ],
        );
    }

    protected function rates(): Attribute
    {
        return Attribute::make(
            get: fn ($value, array $attributes) => [
                ...$attributes['google_rate'] ? ['google' => (float) $attributes['google_rate']] : [],
                ...$attributes['reviews_count'] ? ['reviews_count' => (float) $attributes['reviews_count']] : [],
            ],
        );
    }

    public function baseToArray(): array
    {
        return $this->withoutRecursion(
            fn () => array_merge($this->attributesToArray(), $this->relationsToArray()),
            fn () => $this->attributesToArray(),
        );
    }

    public function reels(): BelongsToMany
    {
        return $this->belongsToMany(Reel::class, 'reel_location');
    }

    public function reelsByPlace(): BelongsToMany
    {
        return $this->belongsToMany(Reel::class, 'reel_location')->where(function ($query) {
            $query->whereNull('creatable_type') // no morph attached
                ->orWhere('creatable_type', '!=', Creator::class); // morph not a Creator
        });
    }

    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }

    public function deals(): HasMany
    {
        return $this->hasMany(Deal::class, 'partner_location_id');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    public function user_deals(): HasManyThrough
    {
        return $this->hasManyThrough(UserDeal::class, Deal::class, 'partner_location_id');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'partner_location_tag');
    }

    public function mealTimes(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::MEAL_TIMES->value)
        );
    }

    public function serviceOptions(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::SERVICE_OPTIONS->value)
        );
    }

    public function dietary(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::DIETARY->value)
        );
    }

    public function cuisineTypes(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::CUISINE_TYPES->value)
        );
    }

    public function ambiance(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::AMBIANCE->value)
        );
    }

    public function specialities(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::SPECIALITIES->value)
        );
    }

    public function parking(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::PARKING->value)
        );
    }

    public function cravings(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::CRAVINGS->value)
        );
    }

    public function area(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::AREA->value)
        );
    }

    public function retailDestination(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $this->tags->where('type', \App\Enums\TagType::RETAIL_DESTINATION->value)
        );
    }

    public function avatar(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')
            ->where('collection_name', 'avatar');
    }

    public function images(): MorphMany
    {
        return $this->media()
            ->where('collection_name', 'images');
    }

    public function menu(): MorphMany
    {
        return $this->media()
            ->where('collection_name', 'menu');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'admin_id');
    }

    public static function getReplicateAction(): Action
    {
        return Action::make('custom_replicate')
            ->label('Replicate')
            ->slideOver()
            ->modalWidth(MaxWidth::SevenExtraLarge)
            ->form(fn (Form $form) => PartnerPlaceResource::form($form->model(PartnerLocation::class)))
            ->fillForm(function (PartnerLocation $partnerLocation) {
                $partnerLocation->load([
                    'deals.dealSlots',
                ]);

                $basicData = Arr::except($partnerLocation->baseToArray(), [
                    'id',
                    'name',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'state',
                    'postal_code',
                    'country',
                    'lat',
                    'lng',
                    'area',
                    'retailDestination',
                    'media',
                    'avatar',
                    'media',
                    'menu',
                    'admin_id',
                ]);

                return array_merge($basicData, $partnerLocation->tags->groupBy('type')->mapWithKeys(function (Collection $tags, $type) {
                    return [$type => $tags->pluck('id')->toArray()];
                })->toArray());
            })
            ->action(function (array $data) {
                $tags = collect($data[\App\Enums\TagType::AMBIANCE->value] ?? [])
                    ->merge($data[\App\Enums\TagType::MEAL_TIMES->value] ?? [])
                    ->merge($data[\App\Enums\TagType::SPECIALITIES->value] ?? [])
                    ->merge($data[\App\Enums\TagType::SERVICE_OPTIONS->value] ?? [])
                    ->merge($data[\App\Enums\TagType::DIETARY->value] ?? [])
                    ->merge($data[\App\Enums\TagType::PARKING->value] ?? [])
                    ->merge($data[\App\Enums\TagType::CUISINE_TYPES->value] ?? [])
                    ->merge($data[\App\Enums\TagType::CRAVINGS->value] ?? [])
                    ->merge($data[\App\Enums\TagType::AREA->value] ?? [])
                    ->merge($data[\App\Enums\TagType::RETAIL_DESTINATION->value] ?? []);

                unset($data[\App\Enums\TagType::AMBIANCE->value]);
                unset($data[\App\Enums\TagType::MEAL_TIMES->value]);
                unset($data[\App\Enums\TagType::SPECIALITIES->value]);
                unset($data[\App\Enums\TagType::SERVICE_OPTIONS->value]);
                unset($data[\App\Enums\TagType::DIETARY->value]);
                unset($data[\App\Enums\TagType::PARKING->value]);
                unset($data[\App\Enums\TagType::CUISINE_TYPES->value]);
                unset($data[\App\Enums\TagType::CRAVINGS->value]);
                unset($data[\App\Enums\TagType::AREA->value]);
                unset($data[\App\Enums\TagType::RETAIL_DESTINATION->value]);

                try {
                    $data['admin_id'] = auth()->id();

                    $partnerLocation = PartnerLocation::query()->create($data);

                    $partnerLocation->tags()->attach($tags);

                } catch (\Throwable $exception) {
                    report($exception);

                    throw ValidationException::withMessages([
                        'mountedTableActionsData.name' => 'could not replicate the place something went wrong',
                    ]);
                }
            });
    }

    public function employees(): BelongsToMany
    {
        return $this->belongsToMany(Employee::class, 'employee_place', 'place_id');
    }

    protected function isPlaceInCollection(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (! Auth::check()) {
                    return false;
                }
                /** @var \App\Models\User $user */
                $user = Auth::user();

                return $user->ownedCollections()
                    ->whereHas('items', function ($query) {
                        $query->where('collectable_type', self::class)
                            ->where('collectable_id', $this->id);
                    })
                    ->exists();
            }
        );
    }

    protected function savedPlacesCount(): Attribute
    {
        return Attribute::make(
            get: function () {

                // TODO replace self::class with $this->getMorphClass()
                return CollectionItem::where('collectable_type', self::class)
                    ->where('collectable_id', $this->id)
                    ->count();
            }
        );
    }

    public function shouldBeSearchable(): bool
    {
        return $this->is_searchable;
    }
}
