<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\MediaCollections\Models\Media as BaseMedia;

/** @property string $full_url */
class Media extends BaseMedia
{
    /** @use HasFactory<\Database\Factories\MediaFactory> */
    use HasFactory;

    protected function fullUrl(): Attribute
    {
        return Attribute::make(
            get: fn ($value, array $attributes) => $this->getFullUrl(),
        );
    }
}
