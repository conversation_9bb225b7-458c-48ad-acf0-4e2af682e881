<?php

namespace App\Actions\Deal;

use App\Enums\DealType;
use App\Models\Deal;
use App\Models\Tag;
use App\Models\User;
use App\Models\UserDeal;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ReserveDeal
{
    public static function handle(string|int $dealId, array $slot, string|int|null $myDealToRenew): UserDeal
    {
        /** @var Deal|null $deal */
        $deal = Deal::find($dealId);

        if (! $deal) {
            throw new \GraphQL\Error\UserError('Sorry the original deal has been deleted');
        }

        /** @var User $user */
        $user = auth()->user();

        // Don't allow user to reserve a deal if they already have an active deal at the same restaurant
        $existingRestaurantDeal = UserDeal::query()
            ->where('user_id', $user->id)
            ->whereHas('deal', function ($query) use ($deal) {
                $query->where('partner_location_id', $deal->partner_location_id);
            })
            ->whereNotIn('status', ['redeemed', 'no-show'])
            ->exists();

        if ($existingRestaurantDeal) {
            throw new \GraphQL\Error\UserError('You can only book one deal per restaurant at a time. Redeem or cancel the other deal to book this deal');
        }

        $date = $slot['date'];
        $date = $date->toDateString();
        $slot['date'] = $date;

        /** @var Carbon $slotFrom @var Carbon $slotTo */
        $slotFrom = Carbon::parse($slot['slot']['from']);
        $slotTo = Carbon::parse($slot['slot']['to']);

        // validate future $slot only accepted
        if (now()->gt($slotTo)) {
            throw new \GraphQL\Error\UserError('Invalid reserve slot. The date must be in the future.');
        }

        $weekDay = Carbon::parse($date)->format('w');

        // Validate the slot is allowed
        // if slot [from,to] is not exists in this deal's slots throw an error
        if ($deal->dealSlots()->where('day', $weekDay)->where('from', $slotFrom->format('H:i'))->where('to', $slotTo->format('H:i'))->count() === 0) {
            throw new \GraphQL\Error\UserError('Invalid reserve slot. The slot is not available for this deal');
        }

        // validate we do have available seats this day
        if ($deal->users()->where('status', 'redeemable')->whereJsonContains('reserve_slot->date', $slot['date'])->count() >= $deal->max_usage_per_day) {
            throw new \GraphQL\Error\UserError('There is no available seats left for this deal');
        }

        if (! $deal->available_slots[$weekDay]['available_seats'] > 0) {
            throw new \GraphQL\Error\UserError('There is no available seats left for this deal');
        }

        // Don't allow user to reserve a deal if they already have a deal with the same id
        $existingDeal = UserDeal::query()
            ->where('user_id', $user->id)
            ->where('deal_id', $deal->id)
            ->whereNotIn('status', ['redeemed', 'no-show'])
            ->where('reuse_after', '>=', now())
            ->exists();

        if ($existingDeal) {
            throw new \GraphQL\Error\UserError('You already have a deal with this id');
        }

        /** @var DealType $dealType */
        $dealType = $deal->deal_type;

        /** @var Collection<?Tag> $serviceTypes */
        $serviceTypes = $deal->service_options;

        $myDeal = UserDeal::query()->create([
            'user_id' => $user->id,
            'deal_id' => $deal->id,
            'reserved_at' => now(),
            'status' => now()->isBetween($slotFrom, $slotTo) ? 'redeemable' : 'upcoming',
            'reserve_slot' => $slot,
            'reuse_after' => $slotTo->addDays($deal->reuse_limit_days),
            'user_deal_id' => $myDealToRenew,
            'deal_snapshot' => [
                'title' => $deal->title,
                'description' => $deal->description,
                'deal_type' => $dealType->name,
                'service_options' => $serviceTypes->map(fn (Tag $tag) => [
                    'id' => $tag->id,
                    'title' => $tag->title,
                ])->toArray(),
                'max_saving' => $deal->max_saving,
                'max_usage_per_day' => $deal->max_usage_per_day,
                'reuse_limit_days' => $deal->reuse_limit_days,
            ],
        ]);

        if (! $myDeal->exists) {
            throw new \GraphQL\Error\UserError('Failed to reserve deal');
        }

        return $myDeal;
    }
}
