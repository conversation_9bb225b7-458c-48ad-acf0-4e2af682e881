<?php

declare(strict_types=1);

namespace App\Actions\Review;

use App\Models\PartnerLocation;
use App\Models\Review;

class CalculateLocationRatingAction
{
    public static function handle(int $partnerLocationId): void
    {
        $partnerLocation = PartnerLocation::findOrFail($partnerLocationId);

        // Calculate average rating and review count
        $reviewStats = Review::where('partner_location_id', $partnerLocationId)
            ->selectRaw('AVG(rating) as average_rating, COUNT(*) as review_count')
            ->first();

        $averageRating = $reviewStats->average_rating ? round($reviewStats->average_rating, 2) : null;
        $reviewCount = $reviewStats->review_count ?? 0;

        // Update partner location with calculated values
        $partnerLocation->update([
            'rating' => $averageRating,
            'reviews_count' => $reviewCount,
        ]);
    }
}
