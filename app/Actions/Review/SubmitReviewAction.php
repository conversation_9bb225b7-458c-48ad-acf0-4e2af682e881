<?php

declare(strict_types=1);

namespace App\Actions\Review;

use App\Models\Deal;
use App\Models\Review;
use App\Models\User;
use App\Models\UserDeal;
use GraphQL\Error\UserError;
use Illuminate\Support\Facades\DB;

class SubmitReviewAction
{
    public static function handle(User $user, array $data): Review
    {
        $dealId = $data['deal_id'];
        $deal = Deal::findOrFail($dealId);

        // Validate that user has redeemed this deal
        $userDeal = UserDeal::where('user_id', $user->id)
            ->where('deal_id', $dealId)
            ->whereNotNull('redeemed_at')
            ->first();

        if (! $userDeal) {
            throw new UserError('You can only review deals that you have redeemed.');
        }

        // Check if user has already reviewed this deal
        $existingReview = Review::where('user_id', $user->id)
            ->where('deal_id', $dealId)
            ->first();

        if ($existingReview) {
            throw new UserError('You have already reviewed this deal.');
        }

        return DB::transaction(function () use ($user, $deal, $data) {
            // Create the review
            $review = Review::create([
                'user_id' => $user->id,
                'deal_id' => $deal->id,
                'partner_location_id' => $deal->partner_location_id,
                'rating' => $data['rating'],
                'savings_amount' => $data['savings_amount'] ?? null,
                'would_visit_again' => $data['would_visit_again'],
                'review_text' => $data['review_text'] ?? null,
            ]);

            // Update partner location rating
            CalculateLocationRatingAction::handle($deal->partner_location_id);

            return $review;
        });
    }
}
