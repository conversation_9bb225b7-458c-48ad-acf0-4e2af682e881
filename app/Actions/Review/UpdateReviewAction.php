<?php

declare(strict_types=1);

namespace App\Actions\Review;

use App\Models\Review;
use App\Models\User;
use GraphQL\Error\UserError;
use Illuminate\Support\Facades\DB;

class UpdateReviewAction
{
    public static function handle(User $user, array $data): Review
    {
        $reviewId = $data['id'];
        $review = Review::findOrFail($reviewId);

        // Validate that user owns this review
        if ($review->user_id !== $user->id) {
            throw new UserError('You can only update your own reviews.');
        }

        return DB::transaction(function () use ($review, $data) {
            // Prepare update data (only include fields that are provided)
            $updateData = [];

            if (isset($data['rating'])) {
                $updateData['rating'] = $data['rating'];
            }

            if (array_key_exists('savings_amount', $data)) {
                $updateData['savings_amount'] = $data['savings_amount'];
            }

            if (array_key_exists('would_visit_again', $data)) {
                $updateData['would_visit_again'] = $data['would_visit_again'];
            }

            if (array_key_exists('review_text', $data)) {
                $updateData['review_text'] = $data['review_text'];
            }

            // Update the review
            $review->update($updateData);

            // If rating was updated, recalculate partner location rating
            if (isset($data['rating'])) {
                CalculateLocationRatingAction::handle($review->partner_location_id);
            }

            return $review->fresh();
        });
    }
}
