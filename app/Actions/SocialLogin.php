<?php

namespace App\Actions;

use App\Enums\InvitationStatus;
use App\Enums\SocialLoginStatus;
use App\Models\Invitation;
use App\Models\User;
use App\Services\GenerateAppleToken;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Laravel\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\AbstractProvider;

class SocialLogin
{
    public static function execute(string $provider, string $token, ?string $firstName = null, ?string $lastName = null): array
    {
        if (! config("services.{$provider}.enabled")) {
            throw ValidationException::withMessages([
                'provider' => "{$provider} provider is not enabled",
            ]);
        }

        try {
            $socialUser = self::getSocialUser($provider, $token);

            $email = $socialUser->getEmail();
            $name = self::getSocialUserName($socialUser, $email, $firstName, $lastName);

            $invitation = Invitation::where('email', $email)->first();
            $existingUser = User::where('email', $email)->first();

            if ($existingUser) {
                // Update name if new firstName/lastName provided and different from current
                if (($firstName || $lastName) && $existingUser->name !== $name) {
                    $existingUser->name = $name;
                    $existingUser->save();
                }

                return self::handleExistingUser($existingUser, $invitation, $provider);
            }

            $user = self::createUser($name, $email, $provider, $socialUser);

            return self::handleNewUser($user, $invitation, $provider, $socialUser);

        } catch (\Exception $e) {
            report($e);
            if ($e instanceof ValidationException) {
                throw $e;
            }
            throw ValidationException::withMessages([
                'token' => 'Invalid social authentication.',
            ]);
        }
    }

    private static function getSocialUser(string $provider, string $token): object
    {
        if ($provider === 'apple') {
            config()->set('services.apple.client_secret', GenerateAppleToken::generate());
        }

        /** @var AbstractProvider $providerDriver */
        $providerDriver = Socialite::driver($provider);

        // Add scopes for first and last name based on provider
        if ($provider === 'google') {
            $providerDriver->scopes(['profile', 'email']);
        } elseif ($provider === 'apple') {
            $providerDriver->scopes(['name', 'email']);
        }

        return $providerDriver->userFromToken($token);
    }

    private static function getSocialUserName(object $socialUser, string $email, ?string $firstName = null, ?string $lastName = null): string
    {
        if ($firstName || $lastName) {
            return trim(($firstName ?? '').' '.($lastName ?? ''));
        }

        return ($socialUser->getName() ?? $socialUser->getNickname())
            ?? Str::of($email)->before('@')->toString();
    }

    private static function createAuthResponse(User $user, string $provider, ?string $message = null, SocialLoginStatus $status = SocialLoginStatus::SUCCESS): array
    {
        $response = [
            'token' => $user->createToken($provider)->plainTextToken,
            'user' => $user,
            'status_code' => $status->value,
        ];

        if ($message) {
            $response['message'] = $message;
        }

        return $response;
    }

    private static function handleExpiredInvitation(User $user, Invitation $invitation, string $provider): array
    {
        $invitation->update([
            'status' => InvitationStatus::PENDING,
            'expires_at' => Carbon::now()->addDays((int) config('invitation.expiry_days')),
        ]);

        return self::createAuthResponse(
            user: $user,
            provider: $provider,
            message: 'Your invitation has been renewed and is pending approval.',
            status: SocialLoginStatus::PENDING_INVITATION
        );
    }

    private static function handleExistingUser(User $user, ?Invitation $invitation, string $provider): array
    {
        if (! $invitation) {
            return self::createAuthResponse($user, $provider);
        }

        if ($invitation->isExpired()) {
            return self::handleExpiredInvitation($user, $invitation, $provider);
        }

        if (! $invitation->isApproved()) {
            return self::createAuthResponse(
                user: $user,
                provider: $provider,
                message: 'Your invitation is still pending approval.',
                status: SocialLoginStatus::AWAITING_APPROVAL
            );
        }

        return self::createAuthResponse($user, $provider);
    }

    private static function handleNewUser(User $user, ?Invitation $invitation, string $provider, object $socialUser): array
    {
        if (! $invitation) {
            // Create new pending invitation
            Invitation::create([
                'email' => $user->email,
                'name' => $user->name,
                'provider' => $provider,
                'provider_id' => $socialUser->getId(),
                'status' => InvitationStatus::PENDING,
                'expires_at' => Carbon::now()->addDays((int) config('invitation.expiry_days')),
            ]);

            return self::createAuthResponse(
                user: $user,
                provider: $provider,
                message: 'Your invitation is pending approval. You will be notified when your account is approved.',
                status: SocialLoginStatus::PENDING_INVITATION
            );
        }

        if ($invitation->isExpired()) {
            return self::handleExpiredInvitation($user, $invitation, $provider);
        }

        if (! $invitation->isApproved()) {
            return self::createAuthResponse(
                user: $user,
                provider: $provider,
                message: 'Your invitation is still pending approval.',
                status: SocialLoginStatus::AWAITING_APPROVAL
            );
        }

        return self::createAuthResponse($user, $provider, status: SocialLoginStatus::NEW_USER);
    }

    private static function createUser(string $name, string $email, string $provider, object $socialUser): User
    {
        $emailVerified = ($provider === 'google' && Str::of($email)->endsWith('@gmail.com') || $provider === 'apple')
            ? now()
            : null;

        return User::create([
            'name' => $name,
            'email' => $email,
            'email_verified_at' => $emailVerified,
            'password' => Hash::make(Str::random(12)),
            'provider' => $provider,
            'provider_id' => $socialUser->getId(),
        ]);
    }
}
