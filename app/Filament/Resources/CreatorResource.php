<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CreatorResource\Pages;
use App\Filament\Resources\CreatorResource\RelationManagers\ReelsRelationManager;
use App\Models\Creator;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class CreatorResource extends Resource
{
    protected static ?string $model = Creator::class;

    protected static ?string $slug = 'creators';

    protected static ?string $navigationIcon = 'heroicon-o-user-plus';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Placeholder::make('formatted_name')
                    ->columnSpanFull()
                    ->content(fn (?Creator $creator) => $creator->formatted_name)
                    ->visibleOn(Pages\EditCreator::class),

                Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                TextInput::make('name')
                    ->hint('Deprecated')
                    ->required(),

                TextInput::make('first_name')
                    ->required(),

                TextInput::make('last_name'),

                TextInput::make('username')
                    ->unique(ignoreRecord: true)
                    ->required(),

                TextInput::make('tiktok_url')
                    ->url(),

                TextInput::make('instagram_url')
                    ->url(),

                Textarea::make('bio')
                    ->required()
                    ->maxLength(80)
                    ->rows(3)
                    ->hint('Maximum 80 characters, 3 lines, 30 characters per line')
                    ->helperText('Each line is limited to 30 characters. Maximum 3 lines allowed.')
                    ->live(onBlur: true)
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state) {
                            $lines = explode("\n", $state);
                            $validatedLines = [];

                            foreach ($lines as $line) {
                                if (strlen(trim($line)) > 30) {
                                    $validatedLines[] = substr(trim($line), 0, 30);
                                } else {
                                    $validatedLines[] = trim($line);
                                }
                            }

                            // Limit to 3 lines
                            $validatedLines = array_slice($validatedLines, 0, 3);

                            $newState = implode("\n", $validatedLines);
                            if ($newState !== $state) {
                                $set('bio', $newState);
                            }
                        }
                    })
                    ->dehydrateStateUsing(function ($state) {
                        if (! $state) {
                            return $state;
                        }

                        $lines = explode("\n", $state);
                        $validatedLines = [];

                        foreach ($lines as $line) {
                            $trimmedLine = trim($line);
                            if (strlen($trimmedLine) > 30) {
                                $validatedLines[] = substr($trimmedLine, 0, 30);
                            } else {
                                $validatedLines[] = $trimmedLine;
                            }
                        }

                        // Limit to 3 lines
                        $validatedLines = array_slice($validatedLines, 0, 3);

                        return implode("\n", $validatedLines);
                    }),

                SpatieMediaLibraryFileUpload::make('avatar')
                    ->collection('avatar')
                    ->imageEditor()
                    ->downloadable()
                    ->avatar()
                    ->image(),
            ])
            ->disabled(fn (?Creator $record): bool => $record?->trashed() ?? false); // Disable form if record is trashed
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name (Legacy)')
                    ->searchable()
                    ->sortable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('formatted_name')
                    ->badge(),

                TextColumn::make('first_name'),

                TextColumn::make('last_name'),

                TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('bio')
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        // Only render the tooltip if the column content exceeds the length limit.
                        return $state;
                    })
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                EditAction::make()->hidden(fn ($record) => $record->trashed()),
                DeleteAction::make()->hidden(fn ($record) => $record->trashed()),
                ForceDeleteAction::make()->visible(fn ($record) => $record->trashed()),
                RestoreAction::make()->visible(fn ($record) => $record->trashed()),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->recordUrl(
                fn (?Creator $record): ?string => $record?->trashed() ? null : Pages\EditCreator::getUrl(['record' => $record])
            );
    }

    public static function getRelations(): array
    {
        return [
            ReelsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreators::route('/'),
            'create' => Pages\CreateCreator::route('/create'),
            'edit' => Pages\EditCreator::route('/{record}/edit'),
        ];
    }
}
