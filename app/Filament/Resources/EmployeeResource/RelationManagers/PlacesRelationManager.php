<?php

namespace App\Filament\Resources\EmployeeResource\RelationManagers;

use App\Models\Partner;
use App\Models\PartnerLocation;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PlacesRelationManager extends RelationManager
{
    protected static string $relationship = 'places';

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->description(function (PartnerLocation $partnerLocation) {
                        /** @var Partner $partner */
                        $partner = $partnerLocation->partner;

                        return $partner->name;
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make('attach')
                    ->form(fn (Tables\Actions\AttachAction $action) => [
                        $action->getRecordSelect()
                            ->options(
                                PartnerLocation::query()->get()->map(function (?PartnerLocation $record) {
                                    /** @var Partner $partner */
                                    $partner = $record->partner;

                                    return ['id' => $record->id, 'name' => "$record->name | $partner->name"];
                                })->pluck('name', 'id')
                            )
                            ->getSearchResultsUsing(function (string $search) {
                                return PartnerLocation::query()->where('name', 'LIKE', "%$search%")->orWhereRelation('partner', 'name', 'LIKE', "%$search%")->get()->map(function (?PartnerLocation $record) {
                                    /** @var Partner $partner */
                                    $partner = $record->partner;

                                    return ['id' => $record->id, 'name' => "$record->name | $partner->name"];
                                })->pluck('name', 'id');
                            })
                            ->preload()
                            ->searchable()
                            ->allowHtml(),
                    ]),
            ])
            ->actions([
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                ]),
            ]);
    }
}
