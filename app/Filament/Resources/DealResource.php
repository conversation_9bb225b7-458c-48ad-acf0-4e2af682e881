<?php

namespace App\Filament\Resources;

use App\Enums\ApprovalStatus;
use App\Enums\DealType;
use App\Enums\TagType;
use App\Filament\Resources\DealResource\Pages;
use App\Filament\Resources\DealResource\Pages\CreateDeal;
use App\Filament\Resources\DealResource\RelationManagers\DealSlotsRelationManager;
use App\Filament\Resources\PartnerPlaceResource\Pages\EditPartnerPlace;
use App\Filament\Resources\PartnerPlaceResource\RelationManagers\DealsRelationManager;
use App\Models\Deal;
use App\Models\Tag;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class DealResource extends Resource
{
    protected static ?string $model = Deal::class;

    protected static ?string $slug = 'deals';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        $tags = CheckboxList::make(TagType::SERVICE_OPTIONS->value)
            ->label('Service Types')
            ->columns(3)
            ->minItems(1)
            ->columnSpanFull();

        if ($form->getLivewire() instanceof CreateDeal || $form->getLivewire() instanceof EditPartnerPlace || $form->getLivewire() instanceof DealsRelationManager) {
            $tags->relationship(TagType::SERVICE_OPTIONS->value, 'title');
        } else {
            $tags->options(Tag::query()->where('type', TagType::SERVICE_OPTIONS->value)->pluck('title', 'id'));
        }

        return $form
            ->schema([
                Select::make('deal_type')
                    ->required()
                    ->searchable()
                    ->options(DealType::class),

                $tags,

                TextInput::make('title')
                    ->required()
                    ->maxLength(30)
                    ->helperText('Maximum 30 characters'),

                TextInput::make('description')
                    ->required(),

                TextInput::make('max_saving')
                    ->prefix('AED')
                    ->default(0)
                    ->required()
                    ->numeric(),

                Hidden::make('valid_days')
                    ->default(array_keys(Carbon::getDays())),

                Section::make('Slots')
                    ->statePath('slots')
                    ->columns(2)
                    ->schema(fn (Get $get, Set $set): array => [
                        TableRepeater::make('slot')
                            ->label('')
                            ->relationship('dealSlots')
                            ->columns(2)
                            ->minItems(1)
                            ->headers([
                                Header::make('day'),
                                Header::make('from'),
                                Header::make('to'),
                            ])
                            ->columnSpanFull()
                            ->extraItemActions([
                                Action::make('copy')
                                    ->icon('heroicon-o-document-duplicate')
                                    ->action(fn () => now())
                                    ->form([
                                        CheckboxList::make('days')
                                            ->label('Day')
                                            ->options(Carbon::getDays()),
                                    ])
                                    ->action(function (array $arguments, Repeater $component, array $data) use ($set, $get) {
                                        $oldSlots = $get('../*.slots.slot');

                                        $copiedSlot = $component->getRawItemState($arguments['item']);

                                        $copiedSlots = [];

                                        foreach ($data['days'] as $day) {
                                            $copiedSlot['day'] = $day;
                                            $copiedSlots[] = $copiedSlot;
                                        }

                                        $set('../*.slots.slot', array_values(array_merge($oldSlots[0], $copiedSlots)));
                                    }),
                            ])
                            ->schema([
                                Select::make('day')
                                    ->label('Day')
                                    ->options(Carbon::getDays())
                                    ->searchable()
                                    ->preload(),

                                TimePicker::make('from')
                                    ->seconds(false)
                                    ->label('From'),

                                TimePicker::make('to')
                                    ->seconds(false)
                                    ->label('To'),
                            ]),
                    ]),

                TextInput::make('max_usage_per_day')
                    ->label('Maximum Booking Per Day(Slots)')
                    ->numeric()
                    ->required()
                    ->default(1),

                TextInput::make('reuse_limit_days')
                    ->label('Deal Reuse Limit')
                    ->hint('Set how often customers can reuse this deal')
                    ->placeholder('Days between uses')
                    ->required()
                    ->numeric(),
            ])->disabled(fn (?Deal $record): bool => $record?->trashed() ?? false); // Changed Model to Deal
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->sortable()
                    ->color(fn (?Deal $record) => $record?->trashed() ? 'gray' : null), // Changed Model to Deal
                TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->color(fn (?Deal $record) => $record?->trashed() ? 'gray' : null), // Changed Model to Deal
                TextColumn::make('deal_type')
                    ->searchable()
                    ->sortable()
                    ->color(fn (?Deal $record) => $record?->trashed() ? 'gray' : null), // Changed Model to Deal

                TextColumn::make('service_type.title')
                    ->label('Service Type')
                    ->searchable(),

                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => $state->getColor())
                    ->icon(fn (ApprovalStatus $state): string => $state->getIcon()),

                TextColumn::make('submitted_by.name')
                    ->label('Submitted By')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('submitted_at')
                    ->label('Submitted')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('reviewed_by.name')
                    ->label('Reviewed By')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('reviewed_at')
                    ->label('Reviewed')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                // Approval Actions
                TableAction::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (Deal $record): bool => $record->isPending())
                    ->requiresConfirmation()
                    ->modalHeading('Approve Deal')
                    ->modalDescription('Are you sure you want to approve this deal?')
                    ->action(function (Deal $record) {
                        $record->approve(auth()->id());

                        \Filament\Notifications\Notification::make()
                            ->title('Deal Approved')
                            ->body('The deal has been approved.')
                            ->success()
                            ->send();
                    }),

                TableAction::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn (Deal $record): bool => $record->isPending())
                    ->form([
                        \Filament\Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required()
                            ->placeholder('Please provide a reason for rejection...'),
                    ])
                    ->action(function (Deal $record, array $data) {
                        $record->reject(auth()->id(), $data['rejection_reason']);

                        \Filament\Notifications\Notification::make()
                            ->title('Deal Rejected')
                            ->body('The deal has been rejected.')
                            ->warning()
                            ->send();
                    }),

                TableAction::make('view_changes')
                    ->label('View Changes')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn (Deal $record): bool => $record->isPending())
                    ->modalHeading('Pending Changes')
                    ->modalContent(function (Deal $record) {
                        $pendingData = $record->pending_data ?? [];
                        $changes = [];

                        foreach ($pendingData as $field => $newValue) {
                            $oldValue = $record->getOriginal($field);
                            if ($oldValue != $newValue) {
                                $changes[] = [
                                    'field' => ucfirst(str_replace('_', ' ', $field)),
                                    'old' => $oldValue,
                                    'new' => $newValue,
                                ];
                            }
                        }

                        return view('filament.admin.pending-changes', [
                            'changes' => $changes,
                            'record' => $record,
                        ]);
                    }),

                EditAction::make()->hidden(fn (?Deal $record) => $record?->trashed() ?? false), // Changed Model to Deal
                DeleteAction::make()->hidden(fn (?Deal $record) => $record?->trashed() ?? false), // Changed Model to Deal
                ForceDeleteAction::make()->visible(fn (?Deal $record) => $record?->trashed() ?? false), // Changed Model to Deal
                RestoreAction::make()->visible(fn (?Deal $record) => $record?->trashed() ?? false), // Changed Model to Deal
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->recordUrl(fn (?Deal $record): ?string => ($record?->trashed() || ! Pages\EditDeal::canAccess(['record' => $record])) ? null : Pages\EditDeal::getUrl(['record' => $record])); // Changed Model to Deal
    }

    public static function getRelations(): array
    {
        return [
            DealSlotsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeals::route('/'),
            'create' => Pages\CreateDeal::route('/create'),
            'edit' => Pages\EditDeal::route('/{record}/edit'),
        ];
    }
}
