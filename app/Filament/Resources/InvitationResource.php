<?php

namespace App\Filament\Resources;

use App\Enums\InvitationStatus;
use App\Filament\Resources\InvitationResource\Pages;
use App\Models\Invitation;
use App\Models\User;
use App\Notifications\InvitationApprovedNotification;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class InvitationResource extends Resource
{
    protected static ?string $model = Invitation::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static ?string $navigationGroup = 'User Management';

    public static function canAccess(): bool
    {
        return auth()->guard('admin')->check();
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->guard('admin')->check();
    }

    public static function canDeleteAny(): bool
    {
        return auth()->guard('admin')->check();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true),

                TextInput::make('name')
                    ->required(),

                Select::make('inviter_user_id')
                    ->label('Invited By')
                    ->relationship('inviter', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                Select::make('provider')
                    ->options([
                        'google' => 'Google',
                        'apple' => 'Apple',
                    ])
                    ->required(),

                TextInput::make('provider_id')
                    ->required()
                    ->helperText('The ID from the OAuth provider (e.g., Google ID or Apple ID)'),

                Select::make('status')
                    ->options(InvitationStatus::class)
                    ->required(),

                DateTimePicker::make('expires_at')
                    ->required()
                    ->default(fn () => now()->addDays((int) config('invitation.expiry_days'))),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('inviter.name')
                    ->label('Invited By')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('provider')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'approved',
                        'danger' => 'rejected',
                    ]),
                Tables\Columns\TextColumn::make('expires_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(InvitationStatus::class),
            ])
            ->actions([
                Tables\Actions\Action::make('approve')
                    ->action(function (Invitation $record): void {
                        $record->update(['status' => InvitationStatus::APPROVED]);

                        // Find the user associated with this invitation and send notification
                        $user = User::where('email', $record->email)->first();
                        if ($user && $user->fcm_token) {
                            $user->notify(new InvitationApprovedNotification);
                        }
                    })
                    ->requiresConfirmation()
                    ->hidden(fn (Invitation $record): bool => $record->isApproved())
                    ->color('success')
                    ->icon('heroicon-o-check')
                    ->visible(fn (): bool => auth()->guard('admin')->check()),
                Tables\Actions\Action::make('reject')
                    ->action(function (Invitation $record): void {
                        $record->update(['status' => InvitationStatus::REJECTED]);
                    })
                    ->requiresConfirmation()
                    ->hidden(fn (Invitation $record): bool => $record->status === InvitationStatus::REJECTED)
                    ->color('danger')
                    ->icon('heroicon-o-x-mark')
                    ->visible(fn (): bool => auth()->guard('admin')->check()),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn (): bool => auth()->guard('admin')->check()),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvitations::route('/'),
            'create' => Pages\CreateInvitation::route('/create'),
            'edit' => Pages\EditInvitation::route('/{record}/edit'),
        ];
    }
}
