<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReelResource\Pages;
use App\Filament\Resources\ReelResource\RelationManagers\LocationsRelationManager;
use App\Models\Admin;
use App\Models\Creator;
use App\Models\Reel;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ReelResource extends Resource
{
    protected static ?string $model = Reel::class;

    protected static ?string $slug = 'reels';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('caption'),

                FileUpload::make('url')
                    ->downloadable()
                    ->panelAspectRatio('9:16')
                    ->label('Video')
                    ->disabledOn('edit')
                    ->required(fn ($operation) => $operation === 'create'),

                FileUpload::make('thumbnail'),

                Select::make('approval_status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ])
                    ->default('pending')
                    ->required(),

                Select::make('partner_id')
                    ->disabledOn('edit')
                    ->relationship('partner', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                MorphToSelect::make('creator')
                    ->searchable()
                    ->preload()
                    ->types([
                        MorphToSelect\Type::make(Creator::class)
                            ->titleAttribute('username'),

                        MorphToSelect\Type::make(Admin::class)
                            ->titleAttribute('name'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),

                TextColumn::make('caption')
                    ->searchable()
                    ->limit(45)
                    ->sortable()
                    ->label('Caption'),

                TextColumn::make('approval_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'warning',
                    }),

                TextColumn::make('locations_count')
                    ->label('Locations')
                    ->sortable()
                    ->counts('locations'),

                ImageColumn::make('thumbnail'),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                \Filament\Tables\Actions\Action::make('download')
                    ->icon('heroicon-m-arrow-down-tray')
                    ->label('Download')
                    ->action(function (Reel $reel) {
                        return \Storage::download($reel->url);
                    }),
                \Filament\Tables\Actions\Action::make('change_approval_status')
                    ->icon('heroicon-m-check-badge')
                    ->label('Change Status')
                    ->form([
                        Select::make('approval_status')
                            ->options([
                                'pending' => 'Pending',
                                'approved' => 'Approved',
                                'rejected' => 'Rejected',
                            ])
                            ->required()
                            ->default(function (Reel $record) {
                                return $record->approval_status;
                            }),
                    ])
                    ->action(function (Reel $record, array $data) {
                        $record->update([
                            'approval_status' => $data['approval_status'],
                        ]);
                    })
                    ->successNotification(
                        notification: fn () => \Filament\Notifications\Notification::make()
                            ->success()
                            ->title('Status updated')
                            ->body('The reel approval status has been updated successfully.'),
                    ),
                EditAction::make(),
                DeleteAction::make(),
                RestoreAction::make(),
                ForceDeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReels::route('/'),
            'create' => Pages\CreateReel::route('/create'),
            'edit' => Pages\EditReel::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            LocationsRelationManager::class,
        ];
    }
}
