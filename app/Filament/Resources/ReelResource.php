<?php

namespace App\Filament\Resources;

use App\Enums\ApprovalStatus;
use App\Filament\Resources\ReelResource\Pages;
use App\Filament\Resources\ReelResource\RelationManagers\LocationsRelationManager;
use App\Models\Admin;
use App\Models\Creator;
use App\Models\Reel;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ReelResource extends Resource
{
    protected static ?string $model = Reel::class;

    protected static ?string $slug = 'reels';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('caption'),

                FileUpload::make('url')
                    ->downloadable()
                    ->panelAspectRatio('9:16')
                    ->label('Video')
                    ->disabledOn('edit')
                    ->required(fn ($operation) => $operation === 'create'),

                FileUpload::make('thumbnail'),

                Select::make('partner_id')
                    ->disabledOn('edit')
                    ->relationship('partner', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                MorphToSelect::make('creator')
                    ->searchable()
                    ->preload()
                    ->types([
                        MorphToSelect\Type::make(Creator::class)
                            ->titleAttribute('username'),

                        MorphToSelect\Type::make(Admin::class)
                            ->titleAttribute('name'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),

                TextColumn::make('caption')
                    ->searchable()
                    ->limit(45)
                    ->label('Caption'),

                ImageColumn::make('thumbnail'),

                TextColumn::make('creator.name')
                    ->label('Creator')
                    ->searchable(),

                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => $state->getColor())
                    ->icon(fn (ApprovalStatus $state): string => $state->getIcon()),

                TextColumn::make('submitted_by.name')
                    ->label('Submitted By')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('submitted_at')
                    ->label('Submitted')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('reviewed_by.name')
                    ->label('Reviewed By')
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('reviewed_at')
                    ->label('Reviewed')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                // Approval Actions
                Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (Reel $record): bool => $record->isPending())
                    ->requiresConfirmation()
                    ->modalHeading('Approve Reel')
                    ->modalDescription('Are you sure you want to approve this reel?')
                    ->action(function (Reel $record) {
                        $record->approve(auth()->id());

                        \Filament\Notifications\Notification::make()
                            ->title('Reel Approved')
                            ->body('The reel has been approved.')
                            ->success()
                            ->send();
                    }),

                Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn (Reel $record): bool => $record->isPending())
                    ->form([
                        \Filament\Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required()
                            ->placeholder('Please provide a reason for rejection...'),
                    ])
                    ->action(function (Reel $record, array $data) {
                        $record->reject(auth()->id(), $data['rejection_reason']);

                        \Filament\Notifications\Notification::make()
                            ->title('Reel Rejected')
                            ->body('The reel has been rejected.')
                            ->warning()
                            ->send();
                    }),

                Action::make('view_changes')
                    ->label('View Changes')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn (Reel $record): bool => $record->isPending())
                    ->modalHeading('Pending Changes')
                    ->modalContent(function (Reel $record) {
                        $pendingData = $record->pending_data ?? [];
                        $changes = [];

                        // Only show fields that have actually changed
                        foreach ($pendingData as $field => $newValue) {
                            $oldValue = $record->getOriginal($field);

                            // Only add to changes if values are actually different
                            if ($oldValue != $newValue) {
                                $changes[] = [
                                    'field' => ucfirst(str_replace('_', ' ', $field)),
                                    'old' => $oldValue,
                                    'new' => $newValue,
                                ];
                            }
                        }

                        return view('filament.admin.pending-changes', [
                            'changes' => $changes,
                            'record' => $record,
                        ]);
                    }),

                \Filament\Tables\Actions\Action::make('download')
                    ->icon('heroicon-m-arrow-down-tray')
                    ->label('Download')
                    ->action(function (Reel $reel) {
                        return \Storage::download($reel->url);
                    }),
                EditAction::make(),
                DeleteAction::make(),
                RestoreAction::make(),
                ForceDeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReels::route('/'),
            'create' => Pages\CreateReel::route('/create'),
            'edit' => Pages\EditReel::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            LocationsRelationManager::class,
        ];
    }
}
