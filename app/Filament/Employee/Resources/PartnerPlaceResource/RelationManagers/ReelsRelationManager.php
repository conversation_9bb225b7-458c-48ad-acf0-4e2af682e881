<?php

namespace App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers;

use App\Enums\ApprovalStatus;
use App\Models\Admin;
use App\Models\Creator;
use App\Models\Reel;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ReelsRelationManager extends RelationManager
{
    protected static string $relationship = 'reels';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Approval status indicator
                Placeholder::make('approval_status')
                    ->label('Status')
                    ->content(fn (?Reel $record): string => $record?->approval_status instanceof ApprovalStatus
                            ? $record->approval_status->label()
                            : 'Draft'
                    )
                    ->visible(fn (?Reel $record): bool => $record !== null),

                TextInput::make('caption')
                    ->disabled(fn (?Reel $record): bool => $record?->isPending() ?? false
                    ),

                FileUpload::make('url')
                    ->downloadable()
                    ->panelAspectRatio('9:16')
                    ->label('Video')
                    ->disabledOn('edit')
                    ->required(fn ($operation) => $operation === 'create')
                    ->disabled(fn (?Reel $record): bool => $record?->isPending() ?? false
                    ),

                FileUpload::make('thumbnail')
                    ->disabled(fn (?Reel $record): bool => $record?->isPending() ?? false
                    ),

                // Partner field - read-only for staff (inherited from parent place)
                Select::make('partner_id')
                    ->relationship('partner', 'name')
                    ->disabled()
                    ->dehydrated()
                    ->required(),

                // Creator field - set to current employee
                Select::make('creatable_type')
                    ->label('Creator Type')
                    ->options([
                        Creator::class => 'Creator',
                        Admin::class => 'Admin',
                    ])
                    ->default(Admin::class)
                    ->disabled()
                    ->dehydrated(),

                Select::make('creatable_id')
                    ->label('Creator')
                    ->relationship('creator', 'name')
                    ->disabled()
                    ->dehydrated(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('thumbnail')
                    ->size(60),

                TextColumn::make('caption')
                    ->searchable()
                    ->limit(50),

                TextColumn::make('creator.name')
                    ->label('Creator')
                    ->searchable(),

                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => $state->getColor())
                    ->icon(fn (ApprovalStatus $state): string => $state->getIcon()),

                TextColumn::make('submitted_at')
                    ->label('Submitted')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('reviewed_at')
                    ->label('Reviewed')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->headerActions([
                CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Set the partner_id from the parent PartnerLocation
                        $data['partner_id'] = $this->ownerRecord->partner->id;
                        // Set creator to current admin/employee
                        $data['creatable_type'] = Admin::class;
                        $data['creatable_id'] = auth()->id();

                        return $data;
                    }),

                // Keep the bulk upload action
                Reel::getUploadReelsAction(),
            ])
            ->actions([
                EditAction::make(),

                // Submit for Approval Action
                Action::make('submit_for_approval')
                    ->label('Submit for Approval')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('success')
                    ->visible(fn (Reel $record): bool => $record->canBeEdited())
                    ->requiresConfirmation()
                    ->modalHeading('Submit Reel for Approval')
                    ->modalDescription('Are you sure you want to submit this reel for admin approval?')
                    ->action(function (Reel $record) {
                        $record->submitForApproval($record->toArray(), auth()->id());

                        Notification::make()
                            ->title('Reel Submitted for Approval')
                            ->body('Your reel has been submitted for admin approval.')
                            ->success()
                            ->send();
                    }),

                // Withdraw Submission Action
                Action::make('withdraw_submission')
                    ->label('Withdraw Submission')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn (Reel $record): bool => $record->isPending())
                    ->requiresConfirmation()
                    ->modalHeading('Withdraw Submission')
                    ->modalDescription('Are you sure you want to withdraw your submission?')
                    ->action(function (Reel $record) {
                        $record->withdraw();

                        Notification::make()
                            ->title('Submission Withdrawn')
                            ->body('Your submission has been withdrawn. You can now make new changes.')
                            ->success()
                            ->send();
                    }),
            ])
            ->defaultSort('updated_at', 'desc');
    }
}
