<?php

namespace App\Filament\Employee\Resources\PartnerPlaceResource\Pages;

use App\Filament\Employee\Resources\PartnerPlaceResource;
use App\Filament\Traits\InteractsWithTags;
use App\Models\PartnerLocation;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditPartnerPlace extends EditRecord
{
    use InteractsWithTags;

    protected static string $resource = PartnerPlaceResource::class;

    protected function getHeaderActions(): array
    {
        /** @var PartnerLocation $record */
        $record = $this->record;

        return [
            // Submit for Approval Action
            Action::make('submit_for_approval')
                ->label('Submit for Approval')
                ->icon('heroicon-o-paper-airplane')
                ->color('success')
                ->visible(fn (): bool => $record->canBeEdited())
                ->requiresConfirmation()
                ->modalHeading('Submit Changes for Approval')
                ->modalDescription('Are you sure you want to submit these changes for admin approval? You will not be able to edit until the admin reviews your submission.')
                ->action(function () {
                    $this->submitForApproval();
                }),

            // Withdraw Submission Action
            Action::make('withdraw_submission')
                ->label('Withdraw Submission')
                ->icon('heroicon-o-arrow-uturn-left')
                ->color('warning')
                ->visible(fn (): bool => $record->isPending())
                ->requiresConfirmation()
                ->modalHeading('Withdraw Submission')
                ->modalDescription('Are you sure you want to withdraw your submission? This will revert to the original approved data.')
                ->action(function () {
                    $this->withdrawSubmission();
                }),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Clear form tags before saving
        $data = $this->clearFormTags($data);

        return $data;
    }

    protected function submitForApproval(): void
    {
        /** @var PartnerLocation $record */
        $record = $this->record;

        // Validate the form first
        $this->form->validate();

        // Get the form data
        $formData = $this->form->getState();
        $formData = $this->clearFormTags($formData);

        // Get the tags data
        $tags = $this->getFormTags($this->form->getState());

        // Submit for approval with the form data
        $record->submitForApproval($formData, auth()->id());

        // Store tags in pending_data as well
        $pendingData = $record->pending_data ?? [];
        $pendingData['tags'] = $tags->toArray();
        $record->update(['pending_data' => $pendingData]);

        Notification::make()
            ->title('Submitted for Approval')
            ->body('Your changes have been submitted for admin approval.')
            ->success()
            ->send();

        $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
    }

    protected function withdrawSubmission(): void
    {
        /** @var PartnerLocation $record */
        $record = $this->record;

        $record->withdraw();

        Notification::make()
            ->title('Submission Withdrawn')
            ->body('Your submission has been withdrawn. You can now make new changes.')
            ->success()
            ->send();

        $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
    }

    protected function afterValidate(): void
    {
        // Only validate tags if we're not pending approval
        /** @var PartnerLocation $record */
        $record = $this->record;

        if (! $record->isPending()) {
            $tags = $this->getFormTags($this->data);

            if ($tags->unique()->count() < 3) {
                Notification::make()
                    ->warning()
                    ->title('Please Choose at least 3 tags.')
                    ->persistent()
                    ->send();

                $this->halt();
            }
        }
    }

    public function afterSave(): void
    {
        // Only sync tags if we're not in pending status
        /** @var PartnerLocation $record */
        $record = $this->record;

        if (! $record->isPending()) {
            $record->tags()->sync(
                $this->getFormTags($this->data)
            );
        }
    }

    protected function canEdit(): bool
    {
        /** @var PartnerLocation $record */
        $record = $this->record;

        return $record->canBeEdited();
    }

    protected function getFormActions(): array
    {
        // Only show save action if record can be edited
        /** @var PartnerLocation $record */
        $record = $this->record;

        if (! $record->canBeEdited()) {
            return [];
        }

        return parent::getFormActions();
    }
}
