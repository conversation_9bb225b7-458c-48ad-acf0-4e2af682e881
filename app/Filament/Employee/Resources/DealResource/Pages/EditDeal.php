<?php

namespace App\Filament\Employee\Resources\DealResource\Pages;

use App\Filament\Employee\Resources\DealResource;
use App\Models\Deal;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditDeal extends EditRecord
{
    protected static string $resource = DealResource::class;

    protected function getHeaderActions(): array
    {
        /** @var Deal $record */
        $record = $this->record;

        return [
            // Submit for Approval Action
            Action::make('submit_for_approval')
                ->label('Submit for Approval')
                ->icon('heroicon-o-paper-airplane')
                ->color('success')
                ->visible(fn (): bool => $record->canBeEdited())
                ->requiresConfirmation()
                ->modalHeading('Submit Deal for Approval')
                ->modalDescription('Are you sure you want to submit this deal for admin approval? You will not be able to edit until the admin reviews your submission.')
                ->action(function () {
                    $this->submitForApproval();
                }),

            // Withdraw Submission Action
            Action::make('withdraw_submission')
                ->label('Withdraw Submission')
                ->icon('heroicon-o-arrow-uturn-left')
                ->color('warning')
                ->visible(fn (): bool => $record->isPending())
                ->requiresConfirmation()
                ->modalHeading('Withdraw Submission')
                ->modalDescription('Are you sure you want to withdraw your submission? This will revert to the original approved data.')
                ->action(function () {
                    $this->withdrawSubmission();
                }),
        ];
    }

    protected function submitForApproval(): void
    {
        /** @var Deal $record */
        $record = $this->record;

        // Validate the form first
        $this->form->validate();

        // Get the form data
        $formData = $this->form->getState();

        // Submit for approval with the form data
        $record->submitForApproval($formData, auth()->id());

        Notification::make()
            ->title('Deal Submitted for Approval')
            ->body('Your deal has been submitted for admin approval.')
            ->success()
            ->send();

        $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
    }

    protected function withdrawSubmission(): void
    {
        /** @var Deal $record */
        $record = $this->record;

        $record->withdraw();

        Notification::make()
            ->title('Submission Withdrawn')
            ->body('Your submission has been withdrawn. You can now make new changes.')
            ->success()
            ->send();

        $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
    }

    protected function canEdit(): bool
    {
        /** @var Deal $record */
        $record = $this->record;

        return $record->canBeEdited();
    }

    protected function getFormActions(): array
    {
        /** @var Deal $record */
        $record = $this->record;

        // Only show save action if record can be edited
        if (! $record->canBeEdited()) {
            return [];
        }

        return parent::getFormActions();
    }
}
