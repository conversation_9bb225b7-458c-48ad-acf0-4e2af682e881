<?php

namespace App\Filament\Employee\Resources;

use App\Enums\ApprovalStatus;
use App\Enums\TagType;
use App\Filament\Employee\Resources\PartnerPlaceResource\Pages;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\DealsRelationManager;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\ReelsRelationManager;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\UserDealsRelationManager;
use App\Forms\Components\PartnerPlaceTagsInput;
use App\Models\PartnerLocation;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

class PartnerPlaceResource extends Resource
{
    protected static ?string $model = PartnerLocation::class;

    protected static ?string $navigationIcon = 'heroicon-o-bookmark-square';

    protected static ?string $navigationLabel = 'Partner Places';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Partner field - read-only for staff
                Select::make('partner_id')
                    ->relationship('partner', 'name')
                    ->disabled()
                    ->dehydrated()
                    ->required(),

                // Approval status indicator
                Placeholder::make('approval_status')
                    ->label('Status')
                    ->content(fn (?PartnerLocation $record): string =>
                        $record?->approval_status?->getLabel() ?? 'Draft'
                    )
                    ->visible(fn (?PartnerLocation $record): bool => $record !== null),

                // Basic information
                TextInput::make('name')
                    ->required()
                    ->maxLength(20)
                    ->helperText('Maximum 20 characters')
                    ->disabled(fn (?PartnerLocation $record): bool =>
                        $record?->isPending() ?? false
                    ),

                PhoneInput::make('phone')
                    ->label('Phone Number')
                    ->required()
                    ->displayNumberFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->inputNumberFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->defaultCountry('ae')
                    ->initialCountry('ae')
                    ->columnSpanFull()
                    ->disabled(fn (?PartnerLocation $record): bool =>
                        $record?->isPending() ?? false
                    ),

                TextInput::make('price_per_person')
                    ->label('Price Per Person (AED)')
                    ->numeric()
                    ->prefix('AED')
                    ->disabled(fn (?PartnerLocation $record): bool =>
                        $record?->isPending() ?? false
                    ),

                // Tags section
                Section::make('Tags')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                PartnerPlaceTagsInput::make('Meal Times')
                                    ->type(TagType::MEAL_TIMES->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),

                                PartnerPlaceTagsInput::make('Service Options')
                                    ->type(TagType::SERVICE_OPTIONS->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),
                            ]),

                        Grid::make(2)
                            ->schema([
                                PartnerPlaceTagsInput::make('Dietary')
                                    ->type(TagType::DIETARY->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),

                                PartnerPlaceTagsInput::make('Specialities')
                                    ->type(TagType::SPECIALITIES->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),
                            ]),

                        Grid::make(2)
                            ->schema([
                                PartnerPlaceTagsInput::make('Parking')
                                    ->type(TagType::PARKING->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),

                                PartnerPlaceTagsInput::make('Cravings')
                                    ->type(TagType::CRAVINGS->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),
                            ]),

                        Grid::make(2)
                            ->schema([
                                PartnerPlaceTagsInput::make('Cuisine Types')
                                    ->type(TagType::CUISINE_TYPES->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),

                                PartnerPlaceTagsInput::make('ambiance')
                                    ->type(TagType::AMBIANCE->value)
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),
                            ]),

                        PartnerPlaceTagsInput::make('Areas')
                            ->maxItems(1)
                            ->type(TagType::AREA->value)
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),

                        PartnerPlaceTagsInput::make('Retail Destinations')
                            ->maxItems(1)
                            ->type(TagType::RETAIL_DESTINATION->value)
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),
                    ])
                    ->collapsible(),

                // Address section
                Section::make('Address')
                    ->schema([
                        TextInput::make('address_line_1')
                            ->label('Address Line 1')
                            ->columnSpanFull()
                            ->placeholder('Enter street address')
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),

                        TextInput::make('address_line_2')
                            ->label('Address Line 2')
                            ->columnSpanFull()
                            ->placeholder('Apartment, suite, etc. (optional)')
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),

                        Grid::make(3)
                            ->schema([
                                TextInput::make('city')
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),

                                TextInput::make('state')
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),

                                TextInput::make('postal_code')
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),
                            ]),

                        TextInput::make('country')
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),
                    ])
                    ->collapsible(),

                // Location coordinates
                Section::make('Location Coordinates')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('lat')
                                    ->label('Latitude')
                                    ->numeric()
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),

                                TextInput::make('lng')
                                    ->label('Longitude')
                                    ->numeric()
                                    ->disabled(fn (?PartnerLocation $record): bool =>
                                        $record?->isPending() ?? false
                                    ),
                            ]),
                    ])
                    ->collapsible(),

                // Opening hours
                Section::make('Opening Hours')
                    ->schema([
                        TableRepeater::make('opening_hours')
                            ->headers([
                                Header::make('day')->label('Day'),
                                Header::make('from')->label('From'),
                                Header::make('to')->label('To'),
                            ])
                            ->schema([
                                Select::make('day')
                                    ->options([
                                        0 => 'Sunday',
                                        1 => 'Monday',
                                        2 => 'Tuesday',
                                        3 => 'Wednesday',
                                        4 => 'Thursday',
                                        5 => 'Friday',
                                        6 => 'Saturday',
                                    ])
                                    ->required(),

                                TimePicker::make('from')
                                    ->required(),

                                TimePicker::make('to')
                                    ->required(),
                            ])
                            ->default(self::getDefaultOpeningHours())
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),
                    ])
                    ->collapsible(),

                // Media uploads
                Section::make('Media')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('avatar')
                            ->label('Avatar')
                            ->collection('avatar')
                            ->required()
                            ->image()
                            ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                                return Str::random(32).'.'.$file->getClientOriginalExtension();
                            })
                            ->visibility('public')
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),

                        SpatieMediaLibraryFileUpload::make('images')
                            ->label('Images')
                            ->collection('images')
                            ->visibility('public')
                            ->multiple()
                            ->required()
                            ->minFiles(1)
                            ->image()
                            ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                                return Str::random(32).'.'.$file->getClientOriginalExtension();
                            })
                            ->columnSpanFull()
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),

                        SpatieMediaLibraryFileUpload::make('menu')
                            ->label('Menu')
                            ->collection('menu')
                            ->visibility('public')
                            ->multiple()
                            ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                                return Str::random(32).'.'.$file->getClientOriginalExtension();
                            })
                            ->acceptedFileTypes([
                                'application/pdf',
                                'image/*',
                            ])
                            ->columnSpanFull()
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),

                        TextInput::make('menu_url')
                            ->url()
                            ->disabled(fn (?PartnerLocation $record): bool =>
                                $record?->isPending() ?? false
                            ),
                    ])
                    ->collapsible(),
            ]);
    }

    private static function getDefaultOpeningHours(): array
    {
        return collect(Carbon::getDays())->map(function ($day, $key) {
            return [
                'day' => $key,
                'from' => '09:00',
                'to' => '21:00',
            ];
        })->toArray();
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('partner.name')
                    ->searchable(),

                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => $state->getColor())
                    ->icon(fn (ApprovalStatus $state): string => $state->getIcon()),

                TextColumn::make('submitted_at')
                    ->label('Submitted')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('reviewed_at')
                    ->label('Reviewed')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('employees', fn (Builder $query) => $query->where('employee_id', auth()->id()));
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPartnerPlaces::route('/'),
            'create' => Pages\CreatePartnerPlace::route('/create'),
            'edit' => Pages\EditPartnerPlace::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            DealsRelationManager::class,
            ReelsRelationManager::class,
            UserDealsRelationManager::class,
        ];
    }
}
