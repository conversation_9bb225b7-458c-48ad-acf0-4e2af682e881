<?php

namespace App\Filament\Employee\Resources;

use App\Enums\ApprovalStatus;
use App\Enums\DealType;
use App\Enums\TagType;
use App\Filament\Employee\Resources\DealResource\Pages;
use App\Models\Deal;
use App\Models\Tag;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DealResource extends Resource
{
    protected static ?string $model = Deal::class;

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Approval status indicator
                Placeholder::make('approval_status')
                    ->label('Status')
                    ->content(fn (?Deal $record): string => $record?->approval_status instanceof ApprovalStatus
                            ? $record->approval_status->label()
                            : 'Draft'
                    )
                    ->visible(fn (?Deal $record): bool => $record !== null),

                // Deal Type
                Select::make('deal_type')
                    ->required()
                    ->searchable()
                    ->options(DealType::class)
                    ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                    ),

                // Service Types (single choice as per user preference)
                Select::make('service_type_id')
                    ->label('Service Type')
                    ->relationship('service_type', 'title')
                    ->options(Tag::query()->where('type', TagType::SERVICE_OPTIONS->value)->pluck('title', 'id'))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                    ),

                // Basic Information
                TextInput::make('title')
                    ->required()
                    ->maxLength(30)
                    ->helperText('Maximum 30 characters')
                    ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                    ),

                TextInput::make('description')
                    ->required()
                    ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                    ),

                TextInput::make('max_saving')
                    ->prefix('AED')
                    ->default(0)
                    ->required()
                    ->numeric()
                    ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                    ),

                Hidden::make('valid_days')
                    ->default(array_keys(Carbon::getDays())),

                // Slots Section
                Section::make('Slots')
                    ->statePath('slots')
                    ->columns(2)
                    ->schema(fn (Get $get, Set $set): array => [
                        TableRepeater::make('slot')
                            ->label('')
                            ->relationship('dealSlots')
                            ->columns(2)
                            ->minItems(1)
                            ->headers([
                                Header::make('day'),
                                Header::make('from'),
                                Header::make('to'),
                            ])
                            ->schema([
                                Select::make('day')
                                    ->label('Day')
                                    ->options(Carbon::getDays())
                                    ->searchable()
                                    ->preload(),

                                TimePicker::make('from')
                                    ->seconds(false)
                                    ->label('From'),

                                TimePicker::make('to')
                                    ->seconds(false)
                                    ->label('To'),
                            ])
                            ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                            ),
                    ]),

                TextInput::make('max_usage_per_day')
                    ->label('Maximum Booking Per Day(Slots)')
                    ->numeric()
                    ->required()
                    ->default(1)
                    ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                    ),

                TextInput::make('reuse_limit_days')
                    ->label('Deal Reuse Limit')
                    ->hint('Set how often customers can reuse this deal')
                    ->placeholder('Days between uses')
                    ->required()
                    ->numeric()
                    ->disabled(fn (?Deal $record): bool => $record?->isPending() ?? false
                    ),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('deal_type')
                    ->badge(),

                TextColumn::make('service_type.title')
                    ->label('Service Type')
                    ->searchable(),

                TextColumn::make('max_saving')
                    ->label('Max Saving (AED)')
                    ->prefix('AED ')
                    ->sortable(),

                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => $state->getColor())
                    ->icon(fn (ApprovalStatus $state): string => $state->getIcon()),

                TextColumn::make('submitted_at')
                    ->label('Submitted')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('reviewed_at')
                    ->label('Reviewed')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                EditAction::make()->url(fn (Deal $record): string => DealResource::getUrl('edit', ['record' => $record->id])),
                DeleteAction::make(),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeals::route('/'),
            'create' => Pages\CreateDeal::route('/create'),
            'edit' => Pages\EditDeal::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('partner_place.employees', fn (Builder $query) => $query->where('employee_id', auth()->id()));
    }
}
