<?php

namespace Database\Factories;

use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Review>
 */
class ReviewFactory extends Factory
{
    protected $model = Review::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'deal_id' => Deal::factory(),
            'partner_location_id' => PartnerLocation::factory(),
            'rating' => $this->faker->numberBetween(1, 5),
            'savings_amount' => $this->faker->randomFloat(2, 5, 100),
            'would_visit_again' => $this->faker->boolean(70), // 70% chance of true
            'review_text' => $this->faker->optional(0.8)->paragraph(), // 80% chance of having review text
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Create a review with high rating (4-5 stars).
     */
    public function highRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(4, 5),
            'would_visit_again' => true,
        ]);
    }

    /**
     * Create a review with low rating (1-2 stars).
     */
    public function lowRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(1, 2),
            'would_visit_again' => false,
        ]);
    }

    /**
     * Create a review with specific rating.
     */
    public function withRating(int $rating): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $rating,
        ]);
    }

    /**
     * Create a review without review text.
     */
    public function withoutText(): static
    {
        return $this->state(fn (array $attributes) => [
            'review_text' => null,
        ]);
    }

    /**
     * Create a review with specific savings amount.
     */
    public function withSavings(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'savings_amount' => $amount,
        ]);
    }
}
