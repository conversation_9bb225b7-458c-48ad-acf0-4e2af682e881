<?php

namespace Database\Factories;

use App\Enums\TagType;
use App\Models\Area;
use App\Models\Media;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\RetailDestination;
use App\Models\Tag;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class PartnerLocationFactory extends Factory
{
    protected $model = PartnerLocation::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),

            'google_rate' => $this->faker->randomFloat(min: 0, max: 5, nbMaxDecimals: 1),
            'reviews_count' => $this->faker->randomNumber(2),

            'price_per_person' => $this->faker->randomFloat(min: 0, max: 100),

            'opening_hours' => [
                [
                    'day' => \Arr::random(array_keys(Carbon::getDays())),
                    'from' => $this->faker->time('H:i'),
                    'to' => $this->faker->time('H:i'),
                ],
            ],

            'phone' => $this->faker->numberBetween('9705000000000', '9705999999999'),
            'address_line_1' => substr($this->faker->address(), 0, 55),
            'address_line_2' => substr($this->faker->address(), 0, 55),
            'city' => $this->faker->city(),
            'state' => $this->faker->word(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->country(),

            'lat' => $this->faker->latitude(),
            'lng' => $this->faker->longitude(),

            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'partner_id' => Partner::factory(),
            'area_id' => Area::factory(),
            'retail_destination_id' => RetailDestination::factory(),
            'is_searchable' => true,
        ];
    }

    public function openingHours(?int $day = null, ?string $from = null, ?string $to = null): static
    {
        return $this->state(fn (array $attributes) => [
            'opening_hours' => [
                [
                    'day' => $day ?? \Arr::random(array_keys(Carbon::getDays())),
                    'from' => $from ?? $this->faker->time('H:i'),
                    'to' => $to ?? $this->faker->time('H:i'),
                ],
            ],
        ]);
    }

    public function withArea(int $count = 1): static
    {
        return $this->hasAttached(Tag::factory()->category(TagType::AREA)->count($count), relationship: 'tags');
    }

    public function withRetailDestination(int $count = 1): static
    {
        return $this->hasAttached(Tag::factory()->category(TagType::RETAIL_DESTINATION)->count($count), relationship: 'tags');
    }

    public function withTags(int $count = 3): static
    {
        return $this->hasAttached(Tag::factory()->category()->count($count), relationship: 'tags');
    }

    public function withRequiredMedia(): static
    {
        return $this->has(Media::factory()->state(fn ($attributes): array => ['collection_name' => 'avatar']), 'media')
            ->has(Media::factory()->state(fn ($attributes): array => ['collection_name' => 'images'])->count(2), 'media')
            ->has(Media::factory()->state(fn ($attributes): array => ['collection_name' => 'menu'])->count(2), 'media');
    }
}