<?php

namespace Database\Factories;

use App\Models\Creator;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class CreatorFactory extends Factory
{
    protected $model = Creator::class;

    public function definition(): array
    {
        $firstName = $this->faker->firstName();
        $lastName = $this->faker->lastName();

        return [
            'name' => "$firstName $lastName", // Legacy field
            'first_name' => $firstName,
            'last_name' => $lastName,
            'bio' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'username' => $this->faker->word(),
            'user_id' => User::factory(),
        ];
    }

    /**
     * Configure the model factory to create a creator with only first name.
     */
    public function firstNameOnly(): static
    {
        return $this->state(function () {
            $firstName = $this->faker->firstName();

            return [
                'name' => $firstName, // Legacy field
                'first_name' => $firstName,
                'last_name' => null,
            ];
        });
    }
}
