<?php

namespace Database\Factories;

use App\Models\Media;
use Illuminate\Database\Eloquent\Factories\Factory;

class MediaFactory extends Factory
{
    protected $model = Media::class;

    public function definition(): array
    {
        return [
            'conversions_disk' => $this->faker->word(),
            'mime_type' => $this->faker->word(),
            'model_type' => $this->faker->word(),
            'uuid' => $this->faker->uuid(),
            'order_column' => $this->faker->randomNumber(),
            'model_id' => $this->faker->randomNumber(),
            'custom_properties' => $this->faker->words(),
            'manipulations' => $this->faker->words(),
            'file_name' => $this->faker->name(),
            'generated_conversions' => $this->faker->words(),
            'collection_name' => $this->faker->name(),
            'responsive_images' => $this->faker->words(),
            'name' => $this->faker->name(),
            'disk' => $this->faker->word(),
            'size' => $this->faker->randomNumber(),
        ];
    }
}
