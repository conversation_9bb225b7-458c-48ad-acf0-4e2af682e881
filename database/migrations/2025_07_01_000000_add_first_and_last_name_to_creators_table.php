<?php

use App\Models\Creator;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add first_name and last_name to creators table
        Schema::table('creators', function (Blueprint $table) {
            $table->string('first_name')->nullable()->after('name');
            $table->string('last_name')->nullable()->after('first_name');
            if (DB::getDriverName() === 'sqlite') {
                $table->string('formatted_name')
                    ->virtualAs("
                        CASE
                            WHEN last_name IS NULL OR last_name = ''
                            THEN CAST(first_name AS TEXT)
                            ELSE CAST(first_name || '.' || substr(last_name, 1, 1) AS TEXT)
                        END
                    ")
                    ->after('last_name');
            } else {
                $table->string('formatted_name')
                    ->virtualAs("
                        CASE
                            WHEN last_name IS NULL OR last_name = ''
                            THEN CAST(first_name AS CHAR)
                            ELSE CAST(CONCAT(first_name, '.', LEFT(last_name, 1)) AS CHAR)
                        END
                    ")
                    ->after('last_name');
            }
        });

        Creator::withoutEvents(function () {
            Creator::query()->whereNotNull('name')->chunk(100, function ($creators) {
                foreach ($creators as $creator) {
                    $this->migrateNameData($creator);
                }
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove first_name and last_name from creators table
        Schema::table('creators', function (Blueprint $table) {
            $table->dropColumn(['first_name', 'last_name', 'formatted_name']);
        });
    }

    private function migrateNameData(Creator $creator): void
    {
        $nameParts = explode(' ', trim($creator->name), 2);
        $creator->first_name = $nameParts[0];
        $creator->last_name = $nameParts[1] ?? null;
        $creator->save();
    }
};
