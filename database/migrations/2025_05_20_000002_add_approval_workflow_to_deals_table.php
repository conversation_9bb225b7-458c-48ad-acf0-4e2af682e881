<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('deals', function (Blueprint $table) {
            $table->after('service_type_id', function (Blueprint $table) {
                $table->enum('approval_status', ['draft', 'pending', 'approved', 'rejected'])
                    ->default('draft');
                $table->json('pending_data')->nullable();
                $table->foreignId('submitted_by')->nullable()->constrained('employees');
                $table->timestamp('submitted_at')->nullable();
                $table->foreignId('reviewed_by')->nullable()->constrained('admins');
                $table->timestamp('reviewed_at')->nullable();
                $table->text('rejection_reason')->nullable();
            });
        });
    }

    public function down(): void
    {
        Schema::table('deals', function (Blueprint $table) {
            $table->dropColumn([
                'approval_status',
                'pending_data',
                'submitted_by',
                'submitted_at',
                'reviewed_by',
                'reviewed_at',
                'rejection_reason',
            ]);
        });
    }
};
