<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('deal_id')->constrained()->onDelete('cascade');
            $table->foreignId('partner_location_id')->constrained()->onDelete('cascade');
            $table->tinyInteger('rating')->unsigned()->comment('Rating from 1 to 5');
            $table->decimal('savings_amount', 8, 2)->nullable()->comment('Amount saved in AED');
            $table->boolean('would_visit_again')->default(false);
            $table->text('review_text')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['partner_location_id', 'rating']);
            $table->index(['user_id', 'created_at']);
            $table->index(['deal_id', 'created_at']);
            $table->index('rating');

            // Unique constraint to prevent duplicate reviews
            $table->unique(['user_id', 'deal_id'], 'unique_user_deal_review');

            // Note: Check constraints would be added here in production
            // For Laravel 11, we'll rely on model validation instead
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
