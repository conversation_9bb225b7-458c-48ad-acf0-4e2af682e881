# Invitation System Documentation

## Overview

The invitation system is a hybrid approach that combines the existing admin approval system with a new user-to-user invitation system. This allows both admin control and viral growth through user referrals.

## System Components

### 1. Existing Admin Approval System (Preserved)

- New users sign up via social login
- System creates pending invitations automatically
- <PERSON><PERSON> can approve/reject invitations via Filament panel
- Users can only access the app after admin approval

### 2. New User-to-User Invitation System

- Authenticated users can generate invitation links
- Each user has a configurable invitation limit (default: 5)
- Invitation links contain tokens and expire after 7 days
- Users can track their sent invitations and remaining quota

## Technical Implementation

### Database Schema

The `invitations` table has been extended with:

- `inviter_user_id`: Foreign key to the user who sent the invitation
- `invitee_user_id`: Foreign key to the user who accepted the invitation
- `token`: Unique token for invitation links
- `token_expires_at`: Expiration date for invitation tokens
- `is_used`: Boolean flag indicating if invitation was used

### GraphQL API

#### User Type Extension

```graphql
type User {
    # ... existing fields
    invitations: UserInvitations!
}

type UserInvitations {
    data: [Invitation!]!
    info: InvitationInfo!
}

type InvitationInfo {
    total: Int! # Total invitations allowed (from config)
    remaining: Int! # Remaining invitations available
}
```

#### Mutations

**Create Invitation**

```graphql
mutation {
    inviteUser # Returns invitation link
}
```

**Accept Invitation**

```graphql
mutation {
    acceptInvitation(token: String!, inviterUserId: ID!) {
        user: User!
        statusCode: Int!
    }
}
```

## User Flow

### Sending Invitations

1. User calls `inviteUser` mutation
2. System checks if user has remaining invitations
3. If available, generates unique token and creates invitation record
4. Returns invitation link: `{baseUrl}/invite?token={token}&inviter={userId}`

### Accepting Invitations

1. New user clicks invitation link
2. User signs up via social login (creates pending admin invitation)
3. User calls `acceptInvitation` mutation with token and inviter ID
4. System validates token and checks limits
5. If valid, marks invitation as used and updates admin invitation with referral

## Configuration

### Environment Variables

```env
USER_INVITATION_LIMIT=5           # Max invitations per user
INVITATION_TOKEN_EXPIRY_DAYS=7    # Token expiration in days
INVITATION_EXPIRY_DAYS=30         # Admin invitation expiry
```

### Config File (`config/invitation.php`)

```php
return [
    'user_invitation_limit' => env('USER_INVITATION_LIMIT', 5),
    'token_length' => 32,
    'expiry_days' => env('INVITATION_EXPIRY_DAYS', 30),
];
```

## Business Logic

### Invitation Limits

- Users can create up to 5 invitations (configurable)
- Only registered users (completed invitations) count against the limit
- Unused/expired invitations don't count against the limit

### Error Handling

- **400**: User has reached invitation limit
- **401**: Invitation token expired or already used
- **404**: Invalid invitation token or inviter not found

### Integration with Existing System

- If user has pending admin invitation, accepting user invitation sets referral and approves admin invitation
- Admin approval system remains unchanged for non-referred users
- Admins can still see and manage all invitations via Filament panel

## Security Considerations

- Invitation tokens are 32-character random strings
- Tokens are unique and checked for duplicates
- Tokens expire after 7 days
- User limits prevent spam/abuse
- All mutations require authentication

## Testing

Comprehensive test suite covers:

- User invitation creation and limits
- Token validation and expiration
- Integration with social login system
- Admin invitation referral tracking
- Error handling for all edge cases

## Future Enhancements

Potential improvements:

- Email notifications for invitations
- Invitation analytics and tracking
- Dynamic invitation limits based on user activity
- Invitation rewards/incentives system
- Bulk invitation management
