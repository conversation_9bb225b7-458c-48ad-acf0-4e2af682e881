includes:
    - vendor/larastan/larastan/extension.neon
    - vendor/nesbot/carbon/extension.neon

parameters:
    stubFiles:
        - vendor/nuwave/lighthouse/_ide_helper.php

    # Turn off some overly strict checks for PHPDoc types
    treatPhpDocTypesAsCertain: false

    paths:
        - app/
        - bootstrap/
        - config/
        - database/
        - graphql/
        - public/
        - resources/
        - routes/
        - tests/

    # Level 10 is the highest level
    level: 5
    ignoreErrors:
        -
            message: '#Access to an undefined property PHPUnit\\Framework\\TestCase::#'
            path: */tests/Feature/GraphQL/Mutations/Deal/*.php
        -
            message: '#Undefined variable: \$this#'
            path: */tests/Pest.php
        # Ignore "always true/false" warnings for enum comparisons in traits
        -
            message: '#(equal|identical|instanceof)\.always(True|False)#'
            paths:
                - */app/Traits/HasApprovalWorkflow.php
                - */app/Filament/Employee/Resources/DealResource/Pages/EditDeal.php
                - */app/Filament/Resources/DealResource.php
        # Ignore "always true" warnings for defensive programming checks
        -
            message: '#function\.alreadyNarrowedType#'
            path: '*/app/Filament/Resources/DealResource.php'
        # Ignore isset checks on non-nullable properties
        -
            message: '#isset\.property#'
            path: '*/app/Traits/HasApprovalWorkflow.php'
        # Ignore overly strict factory assignment warnings in tests
        -
            message: '#Property.*does not accept.*Illuminate\\Database\\Eloquent\\Model#'
            path: */tests/*
        # Ignore test method calls that PHPStan doesn't recognize
        -
            message: '#Call to method.*on an unknown class static#'
            path: */tests/*
        # Ignore test method calls on TestResponse
        -
            message: '#Call to an undefined method.*TestResponse::#'
            path: */tests/*
        # Ignore access to undefined properties in tests (factory-generated models)
        -
            message: '#Access to an undefined property.*Illuminate\\Database\\Eloquent\\Model::#'
            path: */tests/*
        # Ignore method calls that expect closures but get models in tests
        -
            message: '#Parameter.*expects.*Closure.*given#'
            path: */tests/*
        # Ignore method format() calls on strings in tests (date formatting)
        -
            message: '#Cannot call method format\(\) on string#'
            path: */tests/*
