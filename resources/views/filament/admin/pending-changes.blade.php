<div class="space-y-4">
    @if (empty($changes))
        <div class="text-center py-8">
            <p class="text-gray-500">No field changes detected.</p>
        </div>
    @else
        <div class="space-y-3">
            @foreach ($changes as $change)
                <div class="border rounded-lg p-4 bg-gray-50">
                    <h4 class="font-semibold text-gray-900 mb-2">{{ $change['field'] }}</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium text-gray-600">Current Value:</label>
                            <div class="mt-1 p-2 bg-red-50 border border-red-200 rounded text-sm">
                                @if (is_array($change['old']))
                                    @if (empty($change['old']))
                                        <span class="text-gray-500">(empty)</span>
                                    @else
                                        <pre class="whitespace-pre-wrap text-xs">{{ json_encode($change['old'], JSON_PRETTY_PRINT) }}</pre>
                                    @endif
                                @else
                                    {{ $change['old'] ?: '(empty)' }}
                                @endif
                            </div>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Proposed Value:</label>
                            <div class="mt-1 p-2 bg-green-50 border border-green-200 rounded text-sm">
                                @if (is_array($change['new']))
                                    @if (empty($change['new']))
                                        <span class="text-gray-500">(empty)</span>
                                    @else
                                        <pre class="whitespace-pre-wrap text-xs">{{ json_encode($change['new'], JSON_PRETTY_PRINT) }}</pre>
                                    @endif
                                @else
                                    {{ $change['new'] ?: '(empty)' }}
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    @if (isset($record->pending_data['tags']))
        <div class="border rounded-lg p-4 bg-gray-50">
            <h4 class="font-semibold text-gray-900 mb-2">Tags Changes</h4>
            <div class="text-sm text-gray-600">
                <p>New tags will be applied upon approval.</p>
            </div>
        </div>
    @endif

    @if ($record->rejection_reason)
        <div class="border rounded-lg p-4 bg-red-50 border-red-200">
            <h4 class="font-semibold text-red-900 mb-2">Previous Rejection Reason</h4>
            <p class="text-red-700 text-sm">{{ $record->rejection_reason }}</p>
        </div>
    @endif
</div>
